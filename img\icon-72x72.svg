<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 72 72" width="72" height="72">
  <defs>
    <linearGradient id="logoGradient72" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="36" cy="36" r="32" fill="url(#logoGradient72)" />
  
  <!-- Speech bubble -->
  <path d="M22 25 Q22 18 29 18 L50 18 Q58 18 58 25 L58 36 Q58 43 50 43 L32 43 L25 50 L29 43 Q22 43 22 36 Z" 
        fill="white" opacity="0.9"/>
  
  <!-- Text inside bubble -->
  <text x="40" y="29" text-anchor="middle" font-family="Arial, sans-serif" font-size="6" font-weight="bold" fill="#667eea">EN</text>
  <text x="40" y="36" text-anchor="middle" font-family="Arial, sans-serif" font-size="6" font-weight="bold" fill="#764ba2">ES</text>
  
  <!-- Phonetic symbols -->
  <text x="36" y="58" text-anchor="middle" font-family="Arial, sans-serif" font-size="4" fill="white" opacity="0.8">/fəˈnɛtɪk/</text>
</svg>
