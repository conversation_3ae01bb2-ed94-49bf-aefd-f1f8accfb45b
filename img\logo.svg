<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="200" height="200">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="100" cy="100" r="90" fill="url(#logoGradient)" />
  
  <!-- Speech bubble -->
  <path d="M60 70 Q60 50 80 50 L140 50 Q160 50 160 70 L160 100 Q160 120 140 120 L90 120 L70 140 L80 120 Q60 120 60 100 Z" 
        fill="white" opacity="0.9"/>
  
  <!-- Text inside bubble -->
  <text x="110" y="80" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#667eea">EN</text>
  <text x="110" y="100" text-anchor="middle" font-family="Arial, sans-serif" font-size="16" font-weight="bold" fill="#764ba2">ES</text>
  
  <!-- Phonetic symbols -->
  <text x="100" y="160" text-anchor="middle" font-family="Arial, sans-serif" font-size="12" fill="white" opacity="0.8">/fəˈnɛtɪk/</text>
</svg>
