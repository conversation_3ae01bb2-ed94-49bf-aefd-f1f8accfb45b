/* Phrases Management Module for INGLESyLATINOS.com */

const PhrasesManager = (function() {
  // State
  let phrases = [];
  let categories = [];
  let currentCategory = null;
  let currentPhraseIndex = 0;
  let practiceMode = false;
  
  // DOM elements
  let elements = {};
  
  // Initialize phrases manager
  const initialize = async () => {
    console.log('Initializing PhrasesManager...');
    
    try {
      // Cache DOM elements
      cacheElements();
      
      // Load data
      await loadData();
      
      // Render UI
      renderUI();
      
      // Set up event listeners
      setupEventListeners();
      
      console.log('PhrasesManager initialized successfully');
      
    } catch (error) {
      console.error('Error initializing PhrasesManager:', error);
      App.showError('Error al cargar las frases');
    }
  };
  
  // Cache DOM elements
  const cacheElements = () => {
    elements = {
      container: document.getElementById('phrases-container'),
      categoryFilter: document.getElementById('category-filter'),
      searchInput: document.getElementById('search-input'),
      practiceBtn: document.getElementById('practice-btn'),
      addPhraseBtn: document.getElementById('add-phrase-btn')
    };
  };
  
  // Load phrases and categories data
  const loadData = async () => {
    try {
      phrases = await DataManager.getAllPhrases();
      categories = await DataManager.getAllCategories();
      
      console.log(`Loaded ${phrases.length} phrases and ${categories.length} categories`);
      
    } catch (error) {
      console.error('Error loading data:', error);
      throw error;
    }
  };
  
  // Render the main UI
  const renderUI = () => {
    if (!elements.container) return;
    
    elements.container.innerHTML = `
      <div class="phrases-header mb-4">
        <div class="row align-items-center">
          <div class="col-md-6">
            <h2>Práctica de Frases</h2>
            <p class="text-muted">Toca la pronunciación fonética para escuchar</p>
          </div>
          <div class="col-md-6 text-md-end">
            <button class="btn btn-primary me-2" id="practice-mode-btn">
              <i class="bi bi-play-circle"></i> Modo Práctica
            </button>
            <button class="btn btn-outline-primary" id="add-phrase-btn">
              <i class="bi bi-plus-circle"></i> Agregar Frase
            </button>
          </div>
        </div>
      </div>
      
      <div class="phrases-filters mb-4">
        <div class="row">
          <div class="col-md-6">
            <select class="form-select form-control-neumorphic" id="category-filter">
              <option value="">Todas las categorías</option>
              ${categories.map(cat => `<option value="${cat.id}">${cat.name}</option>`).join('')}
            </select>
          </div>
          <div class="col-md-6">
            <input type="text" class="form-control form-control-neumorphic" id="search-input" placeholder="Buscar frases...">
          </div>
        </div>
      </div>
      
      <div id="phrases-list">
        <!-- Phrases will be rendered here -->
      </div>
      
      <!-- Practice Mode Modal -->
      <div class="modal fade" id="practice-modal" tabindex="-1">
        <div class="modal-dialog modal-lg">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">Modo Práctica</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="practice-content">
              <!-- Practice content will be loaded here -->
            </div>
          </div>
        </div>
      </div>
      
      <!-- Add Phrase Modal -->
      <div class="modal fade" id="add-phrase-modal" tabindex="-1">
        <div class="modal-dialog">
          <div class="modal-content">
            <div class="modal-header">
              <h5 class="modal-title">Agregar Nueva Frase</h5>
              <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
              <form id="add-phrase-form">
                <div class="mb-3">
                  <label for="phrase-english" class="form-label">Frase en inglés</label>
                  <input type="text" class="form-control form-control-neumorphic" id="phrase-english" required>
                </div>
                <div class="mb-3">
                  <label for="phrase-phonetic" class="form-label">Pronunciación fonética</label>
                  <input type="text" class="form-control form-control-neumorphic" id="phrase-phonetic" required>
                  <div class="form-text">Ejemplo: /ˈhɛloʊ haʊ ɑr ju/</div>
                </div>
                <div class="mb-3">
                  <label for="phrase-spanish" class="form-label">Traducción al español</label>
                  <input type="text" class="form-control form-control-neumorphic" id="phrase-spanish" required>
                </div>
                <div class="mb-3">
                  <label for="phrase-category" class="form-label">Categoría</label>
                  <select class="form-select form-control-neumorphic" id="phrase-category">
                    ${categories.map(cat => `<option value="${cat.id}">${cat.name}</option>`).join('')}
                  </select>
                </div>
                <div class="mb-3">
                  <label for="phrase-difficulty" class="form-label">Dificultad</label>
                  <select class="form-select form-control-neumorphic" id="phrase-difficulty">
                    <option value="beginner">Principiante</option>
                    <option value="intermediate">Intermedio</option>
                    <option value="advanced">Avanzado</option>
                  </select>
                </div>
              </form>
            </div>
            <div class="modal-footer">
              <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancelar</button>
              <button type="submit" form="add-phrase-form" class="btn btn-primary">Agregar Frase</button>
            </div>
          </div>
        </div>
      </div>
    `;
    
    // Update cached elements
    elements.categoryFilter = document.getElementById('category-filter');
    elements.searchInput = document.getElementById('search-input');
    elements.practiceBtn = document.getElementById('practice-mode-btn');
    elements.addPhraseBtn = document.getElementById('add-phrase-btn');
    
    // Render phrases list
    renderPhrasesList();
  };
  
  // Render phrases list
  const renderPhrasesList = () => {
    const listContainer = document.getElementById('phrases-list');
    if (!listContainer) return;
    
    // Filter phrases
    let filteredPhrases = phrases;
    
    if (currentCategory) {
      filteredPhrases = filteredPhrases.filter(p => p.category === currentCategory);
    }
    
    // Search filter
    const searchQuery = elements.searchInput ? elements.searchInput.value.toLowerCase() : '';
    if (searchQuery) {
      filteredPhrases = filteredPhrases.filter(p => 
        p.english.toLowerCase().includes(searchQuery) ||
        p.spanish.toLowerCase().includes(searchQuery) ||
        p.phonetic.toLowerCase().includes(searchQuery)
      );
    }
    
    if (filteredPhrases.length === 0) {
      listContainer.innerHTML = `
        <div class="text-center py-5">
          <i class="bi bi-search display-1 text-muted"></i>
          <h3 class="mt-3">No se encontraron frases</h3>
          <p class="text-muted">Intenta con otros términos de búsqueda o agrega nuevas frases</p>
        </div>
      `;
      return;
    }
    
    // Render phrase cards
    listContainer.innerHTML = filteredPhrases.map(phrase => createPhraseCard(phrase)).join('');
  };
  
  // Create phrase card HTML
  const createPhraseCard = (phrase) => {
    const categoryName = categories.find(c => c.id === phrase.category)?.name || 'Sin categoría';
    
    return `
      <div class="phrase-card" data-phrase-id="${phrase.id}">
        <div class="phrase-category-badge">
          <span class="badge bg-secondary">${categoryName}</span>
          <span class="badge bg-info">${phrase.difficulty || 'beginner'}</span>
        </div>
        
        <div class="phrase-english">${phrase.english}</div>
        
        <div class="phrase-phonetic" onclick="WebSpeechService.speak('${phrase.english.replace(/'/g, "\\'")}')">
          ${phrase.phonetic}
          <i class="bi bi-volume-up ms-2"></i>
        </div>
        
        <div class="phrase-spanish">${phrase.spanish}</div>
        
        <div class="phrase-actions">
          <button class="btn btn-sm btn-outline-primary" onclick="PhrasesManager.editPhrase('${phrase.id}')" title="Editar">
            <i class="bi bi-pencil"></i>
          </button>
          <button class="btn btn-sm btn-outline-danger" onclick="PhrasesManager.deletePhrase('${phrase.id}')" title="Eliminar">
            <i class="bi bi-trash"></i>
          </button>
          <button class="btn btn-sm btn-outline-success" onclick="PhrasesManager.practicePhrase('${phrase.id}')" title="Practicar">
            <i class="bi bi-play"></i>
          </button>
        </div>
      </div>
    `;
  };
  
  // Set up event listeners
  const setupEventListeners = () => {
    // Category filter
    if (elements.categoryFilter) {
      elements.categoryFilter.addEventListener('change', (e) => {
        currentCategory = e.target.value || null;
        renderPhrasesList();
      });
    }
    
    // Search input
    if (elements.searchInput) {
      elements.searchInput.addEventListener('input', debounce(() => {
        renderPhrasesList();
      }, 300));
    }
    
    // Practice mode button
    if (elements.practiceBtn) {
      elements.practiceBtn.addEventListener('click', startPracticeMode);
    }
    
    // Add phrase button
    if (elements.addPhraseBtn) {
      elements.addPhraseBtn.addEventListener('click', showAddPhraseModal);
    }
    
    // Add phrase form
    const addPhraseForm = document.getElementById('add-phrase-form');
    if (addPhraseForm) {
      addPhraseForm.addEventListener('submit', handleAddPhrase);
    }
  };
  
  // Start practice mode
  const startPracticeMode = () => {
    const modal = new bootstrap.Modal(document.getElementById('practice-modal'));
    loadPracticeContent();
    modal.show();
  };
  
  // Load practice content
  const loadPracticeContent = () => {
    const practiceContent = document.getElementById('practice-content');
    if (!practiceContent || phrases.length === 0) return;
    
    const randomPhrase = phrases[Math.floor(Math.random() * phrases.length)];
    
    practiceContent.innerHTML = `
      <div class="practice-phrase-container text-center">
        <div class="practice-phrase-english mb-4">
          <h2>${randomPhrase.english}</h2>
        </div>
        
        <div class="practice-phrase-phonetic mb-4">
          <button class="btn btn-primary btn-lg" onclick="WebSpeechService.speak('${randomPhrase.english.replace(/'/g, "\\'")}')">
            <i class="bi bi-volume-up me-2"></i>
            ${randomPhrase.phonetic}
          </button>
        </div>
        
        <div class="practice-phrase-spanish mb-4">
          <h3 class="text-muted">${randomPhrase.spanish}</h3>
        </div>
        
        <div class="practice-actions">
          <button class="btn btn-outline-primary me-2" onclick="PhrasesManager.loadPracticeContent()">
            <i class="bi bi-arrow-clockwise"></i> Siguiente Frase
          </button>
          <button class="btn btn-success" onclick="PhrasesManager.recordPronunciation()">
            <i class="bi bi-mic"></i> Grabar mi Pronunciación
          </button>
        </div>
      </div>
    `;
  };
  
  // Show add phrase modal
  const showAddPhraseModal = () => {
    const modal = new bootstrap.Modal(document.getElementById('add-phrase-modal'));
    modal.show();
  };
  
  // Handle add phrase form submission
  const handleAddPhrase = async (e) => {
    e.preventDefault();
    
    try {
      App.showLoading();
      
      const formData = new FormData(e.target);
      const phraseData = {
        english: formData.get('phrase-english'),
        phonetic: formData.get('phrase-phonetic'),
        spanish: formData.get('phrase-spanish'),
        category: formData.get('phrase-category'),
        difficulty: formData.get('phrase-difficulty')
      };
      
      // Add phrase to database
      const newPhrase = await DataManager.addPhrase(phraseData);
      
      // Add to local array
      phrases.push(newPhrase);
      
      // Re-render list
      renderPhrasesList();
      
      // Close modal
      const modal = bootstrap.Modal.getInstance(document.getElementById('add-phrase-modal'));
      modal.hide();
      
      // Reset form
      e.target.reset();
      
      App.showSuccess('Frase agregada exitosamente');
      
    } catch (error) {
      console.error('Error adding phrase:', error);
      App.showError('Error al agregar la frase');
    } finally {
      App.hideLoading();
    }
  };
  
  // Utility function for debouncing
  const debounce = (func, wait) => {
    let timeout;
    return function executedFunction(...args) {
      const later = () => {
        clearTimeout(timeout);
        func(...args);
      };
      clearTimeout(timeout);
      timeout = setTimeout(later, wait);
    };
  };
  
  // Public API
  return {
    initialize,
    loadPracticeContent,
    
    // Phrase actions (called from HTML)
    editPhrase: (phraseId) => {
      console.log('Edit phrase:', phraseId);
      // TODO: Implement edit functionality
    },
    
    deletePhrase: async (phraseId) => {
      if (confirm('¿Estás seguro de que quieres eliminar esta frase?')) {
        try {
          await DataManager.deletePhrase(phraseId);
          phrases = phrases.filter(p => p.id !== phraseId);
          renderPhrasesList();
          App.showSuccess('Frase eliminada');
        } catch (error) {
          App.showError('Error al eliminar la frase');
        }
      }
    },
    
    practicePhrase: (phraseId) => {
      const phrase = phrases.find(p => p.id === phraseId);
      if (phrase) {
        WebSpeechService.speak(phrase.english);
      }
    },
    
    recordPronunciation: () => {
      console.log('Record pronunciation feature coming soon');
      App.showSuccess('Función de grabación próximamente');
    }
  };
})();
