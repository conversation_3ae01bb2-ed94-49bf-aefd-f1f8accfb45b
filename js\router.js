/* Client-side Router for INGLESyLATINOS.com */

const Router = (function() {
  // Current route state
  let currentRoute = null;
  let routes = {};
  
  // Page elements
  let pages = {};
  
  // Initialize router
  const initialize = () => {
    console.log('Initializing router...');
    
    // Cache page elements
    cachePages();
    
    // Define routes
    defineRoutes();
    
    // Set up event listeners
    setupEventListeners();
    
    // Handle initial route
    handleInitialRoute();
    
    console.log('Router initialized');
  };
  
  // Cache page elements
  const cachePages = () => {
    pages = {
      landing: document.getElementById('landing-page'),
      auth: document.getElementById('auth-page'),
      phrases: document.getElementById('phrases-page'),
      dashboard: document.getElementById('dashboard-page')
    };
  };
  
  // Define application routes
  const defineRoutes = () => {
    routes = {
      '': {
        page: 'landing',
        title: 'INGLESyLATINOS.com - Aprende inglés con pronunciación perfecta',
        requiresAuth: false,
        handler: loadLandingPage
      },
      'home': {
        page: 'landing',
        title: 'INGLESyLATINOS.com - Aprende inglés con pronunciación perfecta',
        requiresAuth: false,
        handler: loadLandingPage
      },
      'auth': {
        page: 'auth',
        title: 'Iniciar Sesión - INGLESyLATINOS.com',
        requiresAuth: false,
        handler: loadAuthPage
      },
      'login': {
        page: 'auth',
        title: 'Iniciar Sesión - INGLESyLATINOS.com',
        requiresAuth: false,
        handler: (params) => loadAuthPage({ mode: 'login', ...params })
      },
      'register': {
        page: 'auth',
        title: 'Registrarse - INGLESyLATINOS.com',
        requiresAuth: false,
        handler: (params) => loadAuthPage({ mode: 'register', ...params })
      },
      'phrases': {
        page: 'phrases',
        title: 'Práctica de Frases - INGLESyLATINOS.com',
        requiresAuth: true,
        handler: loadPhrasesPage
      },
      'dashboard': {
        page: 'dashboard',
        title: 'Mi Progreso - INGLESyLATINOS.com',
        requiresAuth: true,
        handler: loadDashboardPage
      }
    };
  };
  
  // Set up event listeners
  const setupEventListeners = () => {
    // Handle browser back/forward buttons
    window.addEventListener('popstate', (event) => {
      const route = event.state ? event.state.route : getRouteFromHash();
      navigateToRoute(route, event.state ? event.state.params : {}, false);
    });
    
    // Handle hash changes
    window.addEventListener('hashchange', () => {
      const route = getRouteFromHash();
      navigateToRoute(route, {}, false);
    });
    
    // Handle navigation links
    document.addEventListener('click', (event) => {
      const link = event.target.closest('a[href^="#"]');
      if (link) {
        event.preventDefault();
        const route = link.getAttribute('href').substring(1);
        navigate(route);
      }
    });
  };
  
  // Handle initial route on page load
  const handleInitialRoute = () => {
    const route = getRouteFromHash();
    navigateToRoute(route, {}, false);
  };
  
  // Get route from current hash
  const getRouteFromHash = () => {
    return window.location.hash.substring(1) || '';
  };
  
  // Navigate to a route
  const navigate = (route, params = {}) => {
    navigateToRoute(route, params, true);
  };
  
  // Navigate to route with optional history update
  const navigateToRoute = async (route, params = {}, updateHistory = true) => {
    console.log(`Navigating to route: ${route}`, params);
    
    // Check if route exists
    if (!routes[route]) {
      console.warn(`Route not found: ${route}`);
      route = ''; // Fallback to home
    }
    
    const routeConfig = routes[route];
    
    // Check authentication requirement
    if (routeConfig.requiresAuth && !App.getCurrentUser()) {
      console.log('Route requires authentication, redirecting to login');
      navigate('login');
      return;
    }
    
    try {
      // Show loading
      App.showLoading();
      
      // Hide all pages
      hideAllPages();
      
      // Update browser history
      if (updateHistory) {
        const url = route ? `#${route}` : '';
        history.pushState({ route, params }, routeConfig.title, url);
      }
      
      // Update page title
      document.title = routeConfig.title;
      
      // Update current route
      currentRoute = route;
      App.setCurrentPage(routeConfig.page);
      
      // Call route handler
      if (routeConfig.handler) {
        await routeConfig.handler(params);
      }
      
      // Show target page
      showPage(routeConfig.page);
      
      // Hide loading
      App.hideLoading();
      
      console.log(`Successfully navigated to: ${route}`);
      
    } catch (error) {
      console.error(`Error navigating to route ${route}:`, error);
      App.hideLoading();
      App.showError('Error al cargar la página. Por favor, inténtalo de nuevo.');
    }
  };
  
  // Hide all pages
  const hideAllPages = () => {
    Object.values(pages).forEach(page => {
      if (page) {
        page.classList.add('d-none');
      }
    });
  };
  
  // Show specific page
  const showPage = (pageName) => {
    const page = pages[pageName];
    if (page) {
      page.classList.remove('d-none');
      
      // Trigger scroll animations
      const animatedElements = page.querySelectorAll('.scroll-animate');
      animatedElements.forEach(el => {
        el.classList.add('in-view');
      });
    }
  };
  
  // Route handlers
  const loadLandingPage = async (params) => {
    console.log('Loading landing page');
    // Landing page is static, no additional loading needed
  };
  
  const loadAuthPage = async (params) => {
    console.log('Loading auth page', params);
    
    const mode = params.mode || 'login';
    const authContainer = pages.auth;
    
    if (!authContainer) return;
    
    // Load auth page content
    authContainer.innerHTML = `
      <div class="container py-5">
        <div class="row justify-content-center">
          <div class="col-md-6 col-lg-5">
            <div class="neumorphic-card">
              <div class="text-center mb-4">
                <h2>${mode === 'login' ? 'Iniciar Sesión' : 'Registrarse'}</h2>
                <p class="text-muted">
                  ${mode === 'login' 
                    ? 'Accede a tu cuenta para continuar practicando' 
                    : 'Crea tu cuenta y comienza a aprender'}
                </p>
              </div>
              
              <form id="auth-form">
                ${mode === 'register' ? `
                  <div class="mb-3">
                    <label for="name" class="form-label">Nombre completo</label>
                    <input type="text" class="form-control form-control-neumorphic" id="name" required>
                  </div>
                ` : ''}
                
                <div class="mb-3">
                  <label for="email" class="form-label">Correo electrónico</label>
                  <input type="email" class="form-control form-control-neumorphic" id="email" required>
                </div>
                
                <div class="mb-3">
                  <label for="password" class="form-label">Contraseña</label>
                  <input type="password" class="form-control form-control-neumorphic" id="password" required>
                </div>
                
                ${mode === 'register' ? `
                  <div class="mb-3">
                    <label for="confirmPassword" class="form-label">Confirmar contraseña</label>
                    <input type="password" class="form-control form-control-neumorphic" id="confirmPassword" required>
                  </div>
                ` : ''}
                
                <div class="d-grid mb-3">
                  <button type="submit" class="btn btn-primary btn-lg">
                    ${mode === 'login' ? 'Iniciar Sesión' : 'Registrarse'}
                  </button>
                </div>
                
                <div class="text-center">
                  <p class="mb-0">
                    ${mode === 'login' 
                      ? '¿No tienes cuenta? <a href="#register">Regístrate aquí</a>' 
                      : '¿Ya tienes cuenta? <a href="#login">Inicia sesión aquí</a>'}
                  </p>
                </div>
              </form>
            </div>
          </div>
        </div>
      </div>
    `;
    
    // Set up form handler
    const form = authContainer.querySelector('#auth-form');
    if (form) {
      form.addEventListener('submit', async (e) => {
        e.preventDefault();
        await handleAuthSubmit(mode, form);
      });
    }
  };
  
  const loadPhrasesPage = async (params) => {
    console.log('Loading phrases page');
    
    const phrasesContainer = pages.phrases;
    if (!phrasesContainer) return;
    
    // Load phrases page content
    phrasesContainer.innerHTML = `
      <div class="container py-5">
        <div class="row">
          <div class="col-12">
            <h1 class="text-center mb-4">Práctica de Frases</h1>
            <div id="phrases-container">
              <!-- Phrases will be loaded here -->
            </div>
          </div>
        </div>
      </div>
    `;
    
    // Initialize phrases manager
    if (typeof PhrasesManager !== 'undefined') {
      await PhrasesManager.initialize();
    }
  };
  
  const loadDashboardPage = async (params) => {
    console.log('Loading dashboard page');
    
    const dashboardContainer = pages.dashboard;
    if (!dashboardContainer) return;
    
    // Load dashboard content
    dashboardContainer.innerHTML = `
      <div class="container py-5">
        <div class="row">
          <div class="col-12">
            <h1 class="text-center mb-4">Mi Progreso</h1>
            <div class="row">
              <div class="col-md-6">
                <div class="neumorphic-card">
                  <h3>Estadísticas</h3>
                  <p>Frases practicadas: <strong>0</strong></p>
                  <p>Racha actual: <strong>0 días</strong></p>
                </div>
              </div>
              <div class="col-md-6">
                <div class="neumorphic-card">
                  <h3>Logros</h3>
                  <p>Próximamente...</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    `;
  };
  
  // Handle authentication form submission
  const handleAuthSubmit = async (mode, form) => {
    try {
      App.showLoading();
      
      const formData = new FormData(form);
      const data = Object.fromEntries(formData);
      
      if (mode === 'register') {
        if (data.password !== data.confirmPassword) {
          throw new Error('Las contraseñas no coinciden');
        }
        await AuthService.register(data);
        App.showSuccess('Cuenta creada exitosamente');
      } else {
        await AuthService.login(data);
        App.showSuccess('Sesión iniciada exitosamente');
      }
      
      // Redirect to phrases page
      navigate('phrases');
      
    } catch (error) {
      console.error('Auth error:', error);
      App.showError(error.message || 'Error en la autenticación');
    } finally {
      App.hideLoading();
    }
  };
  
  // Public API
  return {
    initialize,
    navigate,
    getCurrentRoute: () => currentRoute
  };
})();
