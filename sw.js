/* Service Worker for INGLESyLATINOS.com */

const CACHE_NAME = 'inglesylatinos-v1';
const STATIC_CACHE = 'inglesylatinos-static-v1';
const DYNAMIC_CACHE = 'inglesylatinos-dynamic-v1';

// Files to cache immediately
const STATIC_FILES = [
  '/',
  '/index.html',
  '/manifest.json',
  '/css/theme-variables.css',
  '/css/custom-components.css',
  '/css/landing-page.css',
  '/js/app.js',
  '/js/router.js',
  '/js/auth-service.js',
  '/js/data-manager.js',
  '/js/phrases-manager.js',
  '/js/web-speech-service.js',
  '/js/offline-manager.js',
  // Bootstrap CSS and JS will be cached when first requested
  'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css',
  'https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js',
  'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css'
];

// Install event - cache static files
self.addEventListener('install', (event) => {
  console.log('Service Worker: Installing...');
  
  event.waitUntil(
    caches.open(STATIC_CACHE)
      .then((cache) => {
        console.log('Service Worker: Caching static files');
        return cache.addAll(STATIC_FILES);
      })
      .then(() => {
        console.log('Service Worker: Static files cached');
        return self.skipWaiting();
      })
      .catch((error) => {
        console.error('Service Worker: Error caching static files', error);
      })
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', (event) => {
  console.log('Service Worker: Activating...');
  
  event.waitUntil(
    caches.keys()
      .then((cacheNames) => {
        return Promise.all(
          cacheNames.map((cacheName) => {
            if (cacheName !== STATIC_CACHE && cacheName !== DYNAMIC_CACHE) {
              console.log('Service Worker: Deleting old cache', cacheName);
              return caches.delete(cacheName);
            }
          })
        );
      })
      .then(() => {
        console.log('Service Worker: Activated');
        return self.clients.claim();
      })
  );
});

// Fetch event - serve cached files or fetch from network
self.addEventListener('fetch', (event) => {
  const { request } = event;
  const url = new URL(request.url);
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return;
  }
  
  // Skip chrome-extension and other non-http requests
  if (!url.protocol.startsWith('http')) {
    return;
  }
  
  event.respondWith(
    handleFetchRequest(request)
  );
});

// Handle fetch requests with caching strategy
const handleFetchRequest = async (request) => {
  const url = new URL(request.url);
  
  try {
    // Strategy 1: Cache First for static assets
    if (isStaticAsset(request)) {
      return await cacheFirst(request);
    }
    
    // Strategy 2: Network First for API calls and dynamic content
    if (isApiRequest(request) || isDynamicContent(request)) {
      return await networkFirst(request);
    }
    
    // Strategy 3: Stale While Revalidate for other resources
    return await staleWhileRevalidate(request);
    
  } catch (error) {
    console.error('Service Worker: Fetch error', error);
    
    // Return offline fallback if available
    return await getOfflineFallback(request);
  }
};

// Cache First strategy
const cacheFirst = async (request) => {
  const cachedResponse = await caches.match(request);
  
  if (cachedResponse) {
    return cachedResponse;
  }
  
  // Not in cache, fetch from network and cache
  const networkResponse = await fetch(request);
  
  if (networkResponse.ok) {
    const cache = await caches.open(STATIC_CACHE);
    cache.put(request, networkResponse.clone());
  }
  
  return networkResponse;
};

// Network First strategy
const networkFirst = async (request) => {
  try {
    const networkResponse = await fetch(request);
    
    if (networkResponse.ok) {
      // Cache successful responses
      const cache = await caches.open(DYNAMIC_CACHE);
      cache.put(request, networkResponse.clone());
    }
    
    return networkResponse;
    
  } catch (error) {
    // Network failed, try cache
    const cachedResponse = await caches.match(request);
    
    if (cachedResponse) {
      return cachedResponse;
    }
    
    throw error;
  }
};

// Stale While Revalidate strategy
const staleWhileRevalidate = async (request) => {
  const cachedResponse = await caches.match(request);
  
  // Fetch from network in background
  const networkResponsePromise = fetch(request)
    .then((networkResponse) => {
      if (networkResponse.ok) {
        const cache = caches.open(DYNAMIC_CACHE);
        cache.then((c) => c.put(request, networkResponse.clone()));
      }
      return networkResponse;
    })
    .catch(() => {
      // Network failed, ignore
    });
  
  // Return cached version immediately if available
  if (cachedResponse) {
    return cachedResponse;
  }
  
  // Wait for network if no cached version
  return await networkResponsePromise;
};

// Check if request is for static asset
const isStaticAsset = (request) => {
  const url = new URL(request.url);
  
  // CSS, JS, images, fonts
  return /\.(css|js|png|jpg|jpeg|gif|svg|woff|woff2|ttf|eot|ico)$/i.test(url.pathname) ||
         url.hostname.includes('cdn.jsdelivr.net') ||
         url.hostname.includes('fonts.googleapis.com') ||
         url.hostname.includes('fonts.gstatic.com');
};

// Check if request is for API
const isApiRequest = (request) => {
  const url = new URL(request.url);
  return url.pathname.startsWith('/api/');
};

// Check if request is for dynamic content
const isDynamicContent = (request) => {
  const url = new URL(request.url);
  
  // Consider HTML pages as dynamic (except root)
  return url.pathname.endsWith('.html') && url.pathname !== '/' && url.pathname !== '/index.html';
};

// Get offline fallback
const getOfflineFallback = async (request) => {
  const url = new URL(request.url);
  
  // For HTML requests, return cached index.html
  if (request.headers.get('accept')?.includes('text/html')) {
    const cachedIndex = await caches.match('/index.html');
    if (cachedIndex) {
      return cachedIndex;
    }
  }
  
  // For other requests, return a generic offline response
  return new Response(
    JSON.stringify({
      error: 'Offline',
      message: 'No hay conexión a internet'
    }),
    {
      status: 503,
      statusText: 'Service Unavailable',
      headers: {
        'Content-Type': 'application/json'
      }
    }
  );
};

// Background sync for offline actions
self.addEventListener('sync', (event) => {
  console.log('Service Worker: Background sync triggered', event.tag);
  
  if (event.tag === 'phrases-sync') {
    event.waitUntil(syncPhrases());
  }
});

// Sync phrases when back online
const syncPhrases = async () => {
  try {
    console.log('Service Worker: Syncing phrases...');
    
    // In a real app, this would sync with the server
    // For now, we'll just log the sync attempt
    
    // Notify the main app about sync completion
    const clients = await self.clients.matchAll();
    clients.forEach(client => {
      client.postMessage({
        type: 'SYNC_COMPLETE',
        data: { success: true }
      });
    });
    
  } catch (error) {
    console.error('Service Worker: Sync failed', error);
    
    // Notify the main app about sync failure
    const clients = await self.clients.matchAll();
    clients.forEach(client => {
      client.postMessage({
        type: 'SYNC_FAILED',
        data: { error: error.message }
      });
    });
  }
};

// Push notifications (for future use)
self.addEventListener('push', (event) => {
  console.log('Service Worker: Push received');
  
  const options = {
    body: 'Es hora de practicar inglés!',
    icon: '/img/icon-192x192.svg',
    badge: '/img/icon-72x72.svg',
    tag: 'practice-reminder',
    requireInteraction: false,
    actions: [
      {
        action: 'practice',
        title: 'Practicar Ahora'
      },
      {
        action: 'dismiss',
        title: 'Más Tarde'
      }
    ]
  };
  
  event.waitUntil(
    self.registration.showNotification('INGLESyLATINOS.com', options)
  );
});

// Handle notification clicks
self.addEventListener('notificationclick', (event) => {
  console.log('Service Worker: Notification clicked', event.action);
  
  event.notification.close();
  
  if (event.action === 'practice') {
    // Open app to phrases page
    event.waitUntil(
      clients.openWindow('/#phrases')
    );
  }
});

// Message handling from main app
self.addEventListener('message', (event) => {
  console.log('Service Worker: Message received', event.data);
  
  if (event.data.type === 'SKIP_WAITING') {
    self.skipWaiting();
  }
  
  if (event.data.type === 'CACHE_URLS') {
    event.waitUntil(
      cacheUrls(event.data.urls)
    );
  }
});

// Cache specific URLs
const cacheUrls = async (urls) => {
  const cache = await caches.open(DYNAMIC_CACHE);
  
  for (const url of urls) {
    try {
      await cache.add(url);
      console.log('Service Worker: Cached URL', url);
    } catch (error) {
      console.error('Service Worker: Failed to cache URL', url, error);
    }
  }
};
