/* Offline Manager for INGLESyLATINOS.com */

const OfflineManager = (function() {
  // State
  let isOnline = navigator.onLine;
  let pendingOperations = [];
  let syncInProgress = false;
  
  // Initialize offline manager
  const initialize = () => {
    console.log('Initializing OfflineManager...');
    
    // Set up event listeners
    setupEventListeners();
    
    // Load pending operations
    loadPendingOperations();
    
    // Try to sync if online
    if (isOnline) {
      syncPendingData();
    }
    
    console.log('OfflineManager initialized');
  };
  
  // Set up event listeners
  const setupEventListeners = () => {
    // Online/offline events
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    // Visibility change (when user returns to app)
    document.addEventListener('visibilitychange', handleVisibilityChange);
  };
  
  // Handle online event
  const handleOnline = () => {
    console.log('Connection restored');
    isOnline = true;
    updateConnectionStatus();
    syncPendingData();
  };
  
  // Handle offline event
  const handleOffline = () => {
    console.log('Connection lost');
    isOnline = false;
    updateConnectionStatus();
  };
  
  // Handle visibility change
  const handleVisibilityChange = () => {
    if (!document.hidden && isOnline && pendingOperations.length > 0) {
      syncPendingData();
    }
  };
  
  // Update connection status in UI
  const updateConnectionStatus = () => {
    const statusElement = document.getElementById('connection-status');
    if (statusElement) {
      if (isOnline) {
        statusElement.classList.add('d-none');
      } else {
        statusElement.classList.remove('d-none');
      }
    }
    
    // Update any offline indicators
    const offlineIndicators = document.querySelectorAll('.offline-indicator');
    offlineIndicators.forEach(indicator => {
      if (isOnline) {
        indicator.classList.add('d-none');
      } else {
        indicator.classList.remove('d-none');
      }
    });
  };
  
  // Load pending operations from storage
  const loadPendingOperations = async () => {
    try {
      const stored = localStorage.getItem('inglesylatinos_pending_operations');
      if (stored) {
        pendingOperations = JSON.parse(stored);
        console.log(`Loaded ${pendingOperations.length} pending operations`);
      }
    } catch (error) {
      console.error('Error loading pending operations:', error);
      pendingOperations = [];
    }
  };
  
  // Save pending operations to storage
  const savePendingOperations = () => {
    try {
      localStorage.setItem('inglesylatinos_pending_operations', JSON.stringify(pendingOperations));
    } catch (error) {
      console.error('Error saving pending operations:', error);
    }
  };
  
  // Add operation to pending queue
  const queueOperation = (operation) => {
    const queuedOperation = {
      id: generateOperationId(),
      timestamp: new Date().toISOString(),
      ...operation
    };
    
    pendingOperations.push(queuedOperation);
    savePendingOperations();
    
    console.log('Operation queued for sync:', queuedOperation);
    
    // Try to sync immediately if online
    if (isOnline) {
      syncPendingData();
    }
    
    return queuedOperation.id;
  };
  
  // Sync pending data when online
  const syncPendingData = async () => {
    if (!isOnline || syncInProgress || pendingOperations.length === 0) {
      return;
    }
    
    console.log(`Starting sync of ${pendingOperations.length} operations`);
    syncInProgress = true;
    
    try {
      const successfulOperations = [];
      const failedOperations = [];
      
      for (const operation of pendingOperations) {
        try {
          await processOperation(operation);
          successfulOperations.push(operation);
          console.log('Operation synced successfully:', operation.id);
        } catch (error) {
          console.error('Failed to sync operation:', operation.id, error);
          failedOperations.push(operation);
        }
      }
      
      // Remove successful operations
      pendingOperations = failedOperations;
      savePendingOperations();
      
      if (successfulOperations.length > 0) {
        console.log(`Successfully synced ${successfulOperations.length} operations`);
        
        // Show success notification
        if (successfulOperations.length > 1) {
          App.showSuccess(`Se sincronizaron ${successfulOperations.length} cambios`);
        }
      }
      
      if (failedOperations.length > 0) {
        console.warn(`${failedOperations.length} operations failed to sync`);
      }
      
    } catch (error) {
      console.error('Error during sync:', error);
    } finally {
      syncInProgress = false;
    }
  };
  
  // Process individual operation
  const processOperation = async (operation) => {
    switch (operation.type) {
      case 'ADD_PHRASE':
        return await syncAddPhrase(operation);
      case 'UPDATE_PHRASE':
        return await syncUpdatePhrase(operation);
      case 'DELETE_PHRASE':
        return await syncDeletePhrase(operation);
      case 'UPDATE_USER_PROGRESS':
        return await syncUserProgress(operation);
      default:
        throw new Error(`Unknown operation type: ${operation.type}`);
    }
  };
  
  // Sync add phrase operation
  const syncAddPhrase = async (operation) => {
    // In a real app, this would make an API call
    // For MVP, we'll simulate success
    console.log('Syncing add phrase:', operation.data);
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 100));
    
    // In production, you would:
    // const response = await fetch('/api/phrases', {
    //   method: 'POST',
    //   headers: {
    //     'Content-Type': 'application/json',
    //     'Authorization': `Bearer ${AuthService.getToken()}`
    //   },
    //   body: JSON.stringify(operation.data)
    // });
    // 
    // if (!response.ok) {
    //   throw new Error('Failed to sync phrase');
    // }
    
    return true;
  };
  
  // Sync update phrase operation
  const syncUpdatePhrase = async (operation) => {
    console.log('Syncing update phrase:', operation.data);
    await new Promise(resolve => setTimeout(resolve, 100));
    return true;
  };
  
  // Sync delete phrase operation
  const syncDeletePhrase = async (operation) => {
    console.log('Syncing delete phrase:', operation.phraseId);
    await new Promise(resolve => setTimeout(resolve, 100));
    return true;
  };
  
  // Sync user progress operation
  const syncUserProgress = async (operation) => {
    console.log('Syncing user progress:', operation.data);
    await new Promise(resolve => setTimeout(resolve, 100));
    return true;
  };
  
  // Generate operation ID
  const generateOperationId = () => {
    return 'op_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  };
  
  // Check if device has good connection
  const hasGoodConnection = () => {
    if ('connection' in navigator) {
      const connection = navigator.connection;
      return connection.effectiveType === '4g' || connection.effectiveType === '3g';
    }
    return isOnline; // Fallback
  };
  
  // Estimate data usage for operation
  const estimateDataUsage = (operation) => {
    // Rough estimates in bytes
    switch (operation.type) {
      case 'ADD_PHRASE':
      case 'UPDATE_PHRASE':
        return 500; // ~500 bytes for phrase data
      case 'DELETE_PHRASE':
        return 100; // ~100 bytes for delete request
      case 'UPDATE_USER_PROGRESS':
        return 200; // ~200 bytes for progress data
      default:
        return 100;
    }
  };
  
  // Get sync status
  const getSyncStatus = () => {
    return {
      isOnline,
      syncInProgress,
      pendingOperations: pendingOperations.length,
      hasGoodConnection: hasGoodConnection()
    };
  };
  
  // Force sync (for manual sync button)
  const forceSync = async () => {
    if (!isOnline) {
      throw new Error('No hay conexión a internet');
    }
    
    if (pendingOperations.length === 0) {
      App.showSuccess('No hay cambios pendientes por sincronizar');
      return;
    }
    
    App.showLoading();
    try {
      await syncPendingData();
      App.showSuccess('Sincronización completada');
    } catch (error) {
      App.showError('Error durante la sincronización');
      throw error;
    } finally {
      App.hideLoading();
    }
  };
  
  // Clear all pending operations (for reset)
  const clearPendingOperations = () => {
    pendingOperations = [];
    savePendingOperations();
    console.log('Cleared all pending operations');
  };
  
  // Export data for backup
  const exportData = async () => {
    try {
      const phrases = await DataManager.getAllPhrases();
      const categories = await DataManager.getAllCategories();
      const userData = AuthService.getCurrentUser();
      
      const exportData = {
        version: '1.0',
        timestamp: new Date().toISOString(),
        phrases,
        categories,
        userData: userData ? {
          preferences: userData.preferences,
          progress: userData.progress
        } : null,
        pendingOperations
      };
      
      return exportData;
    } catch (error) {
      console.error('Error exporting data:', error);
      throw error;
    }
  };
  
  // Import data from backup
  const importData = async (importData) => {
    try {
      if (!importData.version || !importData.phrases) {
        throw new Error('Invalid import data format');
      }
      
      // Import phrases
      for (const phrase of importData.phrases) {
        await DataManager.addPhrase(phrase);
      }
      
      // Import categories
      if (importData.categories) {
        for (const category of importData.categories) {
          await DataManager.addCategory(category);
        }
      }
      
      console.log('Data imported successfully');
      return true;
    } catch (error) {
      console.error('Error importing data:', error);
      throw error;
    }
  };
  
  // Public API
  return {
    initialize,
    queueOperation,
    syncPendingData,
    getSyncStatus,
    forceSync,
    clearPendingOperations,
    exportData,
    importData,
    isOnline: () => isOnline
  };
})();
