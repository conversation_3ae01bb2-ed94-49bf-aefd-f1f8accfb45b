# Comprehensive Development Plan for INGLESyLATINOS.com Rebuild

## Table of Contents

1. [Executive Summary](#executive-summary)
2. [Foundation: Bootswatch Morph Theme Implementation](#1-foundation-bootswatch-morph-theme-implementation)
   1. [Theme Analysis](#11-theme-analysis)
   2. [Core CSS Variables](#12-core-css-variables)
   3. [Potential Theme Conflicts and Solutions](#13-potential-theme-conflicts-and-solutions)
   4. [Theme Integration Strategy](#14-theme-integration-strategy)
3. [Directory Structure and Files](#2-directory-structure-and-files)
4. [Landing Page Design](#3-landing-page-design)
   1. [Hero Section](#31-hero-section)
   2. [Value Proposition Section](#32-value-proposition-section)
   3. [Call-to-Action Section](#33-call-to-action-section)
   4. [Responsive Design Considerations](#34-responsive-design-considerations)
5. [Core Functionalities](#4-core-functionalities)
   1. [User Authentication System](#41-user-authentication-system)
   2. [Phrase Management](#42-phrase-management)
   3. [Phonetic Pronunciation Features](#43-phonetic-pronunciation-features)
   4. [Video Embedding and Management](#44-video-embedding-and-management)
   5. [Admin Panel and User Roles](#45-admin-panel-and-user-roles)
   6. [Offline Capabilities](#46-offline-capabilities)
6. [Implementation Roadmap](#5-implementation-roadmap)
7. [CSS Implementation Guidelines](#6-css-implementation-guidelines)
   1. [Neumorphic Component Styling](#61-neumorphic-component-styling)
   2. [Custom Component Guidelines](#62-custom-component-guidelines)
8. [JavaScript Architecture](#7-javascript-architecture)
   1. [Module Pattern](#71-module-pattern)
   2. [Service Worker Implementation](#72-service-worker-implementation)
9. [Data Management](#8-data-management)
   1. [IndexedDB Structure](#81-indexeddb-structure)
   2. [API Integration](#82-api-integration)
10. [Authentication System](#9-authentication-system)
    1. [Authentication Service](#91-authentication-service)
    2. [Login and Registration Forms](#92-login-and-registration-forms)
11. [Phrase Management Implementation](#10-phrase-management-implementation)
    1. [Phrase Data Structure](#101-phrase-data-structure)
    2. [Phrase UI Components](#102-phrase-ui-components)
12. [Phonetic Features Implementation](#11-phonetic-features-implementation)
    1. [Web Speech API Integration](#111-web-speech-api-integration)
    2. [Phonetic Text Styling](#112-phonetic-text-styling)
13. [Video Management Implementation](#12-video-management-implementation)
    1. [YouTube API Integration](#121-youtube-api-integration)
    2. [Video UI Components](#122-video-ui-components)
14. [Admin Panel Implementation](#13-admin-panel-implementation)
    1. [Admin Dashboard](#131-admin-dashboard)
    2. [User Management](#132-user-management)
15. [Offline Capabilities Implementation](#14-offline-capabilities-implementation)
    1. [Service Worker Configuration](#141-service-worker-configuration)
    2. [Offline UI Components](#142-offline-ui-components)
16. [Testing and Quality Assurance](#15-testing-and-quality-assurance)
    1. [Testing Strategy](#151-testing-strategy)
    2. [Performance Optimization](#152-performance-optimization)
17. [Deployment and Maintenance](#16-deployment-and-maintenance)
    1. [Deployment Process](#161-deployment-process)
    2. [Maintenance Plan](#162-maintenance-plan)
18. [Conclusion](#17-conclusion)

## Executive Summary

Este plan de desarrollo detalla una reconstrucción completa de INGLESyLATINOS.com utilizando el tema Bootswatch Morph como base. El plan aborda posibles conflictos de estilo desde el principio, establece una estructura de directorios clara, detalla las funcionalidades principales, y proporciona una hoja de ruta de implementación secuencial para garantizar la aplicación consistente del estilo neumórfico del tema Morph en toda la aplicación.

La plataforma se enfoca en tres principios fundamentales:
1. **Diseño mobile-first**: Optimizado para dispositivos móviles con una experiencia fluida en todos los tamaños de pantalla.
2. **Gamificación integral**: Sistema de logros, recompensas y progresión que mantiene a los usuarios motivados.
3. **Práctica de pronunciación**: Herramientas especializadas para mejorar la pronunciación con retroalimentación en tiempo real.

## 1. Foundation: Bootswatch Morph Theme Implementation

### 1.1 Theme Analysis

The Bootswatch Morph theme features a neumorphic design with these key characteristics:
- Soft shadows creating a 3D effect (both raised and inset)
- Rounded corners and pill-shaped buttons
- Light color scheme with subtle gradients
- Minimal borders, relying on shadows for definition
- Custom CSS variables for consistent styling

### 1.2 Core CSS Variables

```css
/* Primary Morph theme variables to preserve */
--bs-body-bg: #d9e3f1;
--bs-body-color: #7b8ab8;
--bs-tertiary-bg: #f0f5fa;
--bs-primary: #378dfc;
--bs-secondary: #d9e3f1;
--bs-border-radius: 0.375rem;
--bs-border-radius-pill: 50rem;
--bs-box-shadow: 5px 5px 10px rgba(55, 94, 148, 0.2), -5px -5px 10px rgba(255, 255, 255, 0.4);
--bs-box-shadow-inset: inset 2px 2px 8px rgba(55, 94, 148, 0.3), inset -3px -2px 5px rgba(255, 255, 255, 0.8);
--bs-box-shadow-lg: 8px 8px 40px rgba(0, 0, 0, 0.15);
--bs-font-sans-serif: Nunito, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
```

### 1.3 Potential Theme Conflicts and Solutions

| Potential Conflict | Solution |
|-------------------|----------|
| Custom component shadows | Use explicit shadow values instead of CSS variables when needed |
| Border implementations | Minimize borders, use shadows for definition |
| Color scheme consistency | Create a color palette document and stick to it |
| Custom form elements | Ensure all form elements follow neumorphic styling |
| Third-party components | Apply custom styling to match neumorphic design |
| Mobile responsiveness | Test shadows and effects on various screen sizes |

### 1.4 Theme Integration Strategy

1. Use the complete, unminified version of bootstrap-morph.css
2. Create a theme-variables.css file to centralize all theme variables
3. Create component-specific CSS files that extend the theme
4. Implement a theme testing page to verify styling consistency

## 2. Directory Structure and Files

```
inglesylatinos/
├── index.html                  # Main application entry point
├── offline.html                # Offline fallback page
├── theme-test.html             # Theme testing page
├── manifest.json               # PWA manifest
├── css/
│   ├── bootstrap-morph.css     # Complete Bootswatch Morph theme
│   ├── theme-variables.css     # Centralized theme variables
│   ├── custom-components.css   # Custom component styling
│   ├── landing-page.css        # Landing page specific styles
│   ├── auth-forms.css          # Authentication form styles
│   ├── admin-panel.css         # Admin panel styles
│   ├── phrases-module.css      # Phrase management styles
│   └── video-module.css        # Video embedding styles
├── js/
│   ├── app.js                  # Main application logic
│   ├── router.js               # Client-side routing
│   ├── auth-service.js         # Authentication service
│   ├── data-manager.js         # Data management service
│   ├── phrases-manager.js      # Phrase management logic
│   ├── video-service.js        # Video embedding logic
│   ├── web-speech-service.js   # Speech synthesis/recognition
│   ├── admin-panel.js          # Admin panel functionality
│   ├── user-settings.js        # User settings management
│   ├── offline-manager.js      # Offline functionality
│   └── sw.js                   # Service worker
├── img/
│   ├── logo/                   # Logo variations
│   ├── icons/                  # Application icons
│   ├── landing/                # Landing page imagery
│   └── illustrations/          # UI illustrations
├── fonts/
│   └── nunito/                 # Nunito font files (optional local copy)
└── pages/
    ├── landing.html            # Landing page content
    ├── auth.html               # Authentication page
    ├── phrases.html            # Phrases management page
    ├── videos.html             # Video content page
    ├── admin.html              # Admin panel page
    └── user-settings.html      # User settings page
```

## 3. Landing Page Design

### 3.1 Hero Section

```html
<section class="hero-section">
  <div class="container">
    <div class="row align-items-center">
      <div class="col-lg-6">
        <h1 class="hero-title">Aprende inglés con pronunciación perfecta</h1>
        <p class="hero-subtitle">Domina la pronunciación del inglés con nuestro sistema de fonética simplificada y videos prácticos</p>
        <div class="hero-cta">
          <a href="#register" class="btn btn-primary btn-lg">Comenzar Ahora</a>
          <a href="#learn-more" class="btn btn-outline-secondary btn-lg ms-3">Saber Más</a>
        </div>
      </div>
      <div class="col-lg-6">
        <div class="hero-image-container">
          <img src="img/landing/hero-image.png" alt="INGLESyLATINOS.com App" class="hero-image">
        </div>
      </div>
    </div>
  </div>
</section>
```

### 3.2 Value Proposition Section

```html
<section class="value-section">
  <div class="container">
    <h2 class="section-title text-center">¿Por qué elegir INGLESyLATINOS.com?</h2>
    <div class="row g-4 mt-4">
      <div class="col-md-4">
        <div class="value-card">
          <div class="value-icon">
            <i class="bi bi-mic"></i>
          </div>
          <h3>Pronunciación Simplificada</h3>
          <p>Sistema fonético diseñado especialmente para hispanohablantes</p>
        </div>
      </div>
      <div class="col-md-4">
        <div class="value-card">
          <div class="value-icon">
            <i class="bi bi-camera-video"></i>
          </div>
          <h3>Videos Prácticos</h3>
          <p>Aprende con videos seleccionados para cada nivel y categoría</p>
        </div>
      </div>
      <div class="col-md-4">
        <div class="value-card">
          <div class="value-icon">
            <i class="bi bi-wifi-off"></i>
          </div>
          <h3>Modo Offline</h3>
          <p>Estudia sin conexión a internet, en cualquier momento y lugar</p>
        </div>
      </div>
    </div>
  </div>
</section>
```

### 3.3 Call-to-Action Section

```html
<section class="cta-section">
  <div class="container">
    <div class="cta-container">
      <h2>Comienza tu viaje hacia la fluidez en inglés</h2>
      <p>Únete a miles de estudiantes que ya están mejorando su pronunciación</p>
      <a href="#register" class="btn btn-primary btn-lg">Registrarse Gratis</a>
    </div>
  </div>
</section>
```

### 3.4 Responsive Design Considerations

- Use Bootstrap's grid system for layout
- Implement custom breakpoints for hero section
- Optimize images for different screen sizes
- Ensure touch-friendly elements for mobile
- Test neumorphic shadows on various devices

## 4. Core Functionalities

### 4.1 User Authentication System

#### Components:
- Registration form
- Login form
- Password recovery
- Email verification
- Profile management
- Session management
- Logros y progreso del usuario

#### Implementation Notes:
- Use JWT for authentication
- Implement secure password storage
- Create neumorphic form styling
- Add form validation with visual feedback
- Store user preferences in local storage
- Sincronización de progreso entre dispositivos

### 4.2 Phrase Management

#### Components:
- Phrase creation/editing
- Categorization system
- Search functionality
- Filtering options
- Bulk actions
- Import/export capabilities
- Sistema de tarjetas de frases con diseño optimizado para móvil

#### Implementation Notes:
- Create neumorphic card design for phrases
- Implement drag-and-drop for organization
- Use custom animations for interactions
- Ensure offline storage capabilities
- Add sharing functionality
- Diseño de tarjetas donde cada frase ocupa la mayoría del espacio de pantalla
- Estructura clara con frase en inglés (H1), pronunciación fonética (H2) y traducción en español (H1 italics)

### 4.3 Phonetic Pronunciation Features

#### Components:
- Phonetic text display con formato destacado
- Audio playback con voces personalizables
- Speech recognition con análisis de precisión
- Practice mode con retroalimentación visual
- Pronunciation feedback en tiempo real
- Sistema de puntuación de pronunciación

#### Implementation Notes:
- Design distinctive phonetic text styling
- Implement Web Speech API integration
- Create visual feedback for pronunciation
- Add progress tracking
- Ensure mobile compatibility
- Opciones de personalización de voz (tipo, tono, velocidad)
- Indicadores visuales de precisión de pronunciación
- Grabación y comparación de pronunciación del usuario

### 4.4 Video Embedding and Management

#### Components:
- Video browser con categorías y niveles
- Category-based organization
- YouTube integration
- Playback controls
- Favorites/bookmarks
- Transcripciones y subtítulos
- Ejercicios relacionados con cada video

#### Implementation Notes:
- Design neumorphic video cards
- Implement lazy loading for performance
- Create custom video controls
- Add offline viewing capabilities
- Implement responsive video containers
- Integración con YouTube para contenido educativo
- Sistema de recomendación basado en nivel y progreso

### 4.5 Gamification System

#### Components:
- Sistema de logros y medallas
- Rastreador de rachas diarias
- Tabla de clasificación
- Niveles de progresión
- Recompensas por consistencia
- Desafíos diarios y semanales
- Insignias por compartir y referir amigos

#### Implementation Notes:
- Diseñar insignias y medallas con estilo neumórfico
- Implementar animaciones de celebración
- Crear notificaciones de logros
- Desarrollar sistema de puntos y experiencia
- Integrar con redes sociales para compartir logros
- Implementar recompensas por rachas de estudio consecutivas
- Diseñar interfaz de progreso visual e intuitiva

### 4.6 Quiz and Practice Modes

#### Components:
- Quiz de traducción (inglés a español)
- Quiz de comprensión (seleccionar respuesta correcta)
- Ejercicios de completar frases
- Práctica de pronunciación con puntuación
- Modo de repaso espaciado
- Modo de desafío cronometrado
- Ejercicios de escucha y selección

#### Implementation Notes:
- Diseñar interfaz intuitiva para diferentes tipos de quiz
- Implementar sistema de puntuación
- Crear retroalimentación inmediata
- Desarrollar algoritmo de repaso espaciado
- Integrar con sistema de logros
- Implementar dificultad adaptativa según rendimiento
- Diseñar modo de práctica sin conexión

### 4.7 Books Section

#### Components:
- Biblioteca de libros en formato PDF
- Enlaces de afiliados de Amazon
- Contenido descargable mediante donación
- Organización por nivel y categoría
- Extractos de muestra
- Guías de estudio complementarias
- Sistema de marcadores y notas

#### Implementation Notes:
- Diseñar interfaz de biblioteca con estilo neumórfico
- Implementar visor de PDF integrado
- Crear sistema de gestión de descargas
- Integrar con Buy Me a Coffee para donaciones
- Desarrollar sistema de recomendación de libros
- Implementar seguimiento de progreso de lectura
- Diseñar interfaz de notas y marcadores

### 4.8 Admin Panel and User Roles

#### Components:
- User management
- Content moderation
- Analytics dashboard
- System settings
- Bulk operations
- Generación de frases con IA
- Gestión de contenido educativo

#### Implementation Notes:
- Create role-based access control
- Design neumorphic admin interface
- Implement data visualization
- Add export functionality
- Create audit logging
- Integrar con Google AI para generación de frases
- Implementar herramientas de importación/exportación masiva

### 4.9 Offline Capabilities

#### Components:
- Service worker implementation
- Cache management
- Offline data synchronization
- Connection status indicator
- Offline-first architecture
- Descarga de lecciones para uso sin conexión
- Sincronización inteligente de progreso

#### Implementation Notes:
- Implement progressive web app features
- Create visual indicators for offline mode
- Design sync management interface
- Add background sync capabilities
- Implement conflict resolution
- Optimizar almacenamiento para dispositivos con capacidad limitada
- Priorizar contenido según patrones de uso del usuario

## 5. Implementation Roadmap

### Phase 1: Foundation (Weeks 1-2)

1. **Project Setup**
   - Create directory structure
   - Initialize Git repository
   - Set up development environment
   - Configure build tools
   - Establecer estándares de código y documentación

2. **Theme Integration**
   - Implement Bootswatch Morph theme
   - Create theme variables file
   - Build theme testing page
   - Document color palette and styling guidelines
   - Crear componentes neumórficos base (tarjetas, botones, inputs)

3. **Core HTML Structure**
   - Create index.html with proper meta tags
   - Implement offline.html
   - Set up PWA manifest
   - Configure service worker skeleton
   - Optimizar para dispositivos móviles desde el inicio

### Phase 2: Landing Page (Weeks 3-4)

1. **Hero Section**
   - Design and implement hero section
   - Create responsive layout
   - Optimize imagery
   - Implement call-to-action buttons
   - Añadir animaciones sutiles para mejorar engagement

2. **Value Proposition**
   - Design value cards with neumorphic styling
   - Implement responsive grid
   - Add animations and interactions
   - Ensure accessibility
   - Destacar características de pronunciación y gamificación

3. **Additional Sections**
   - Implement testimonials section
   - Create features showcase
   - Add FAQ accordion
   - Design footer with contact information
   - Añadir sección de demostración interactiva

### Phase 3: Authentication System (Weeks 5-6)

1. **Authentication Forms**
   - Design neumorphic login form
   - Create registration form
   - Implement password recovery
   - Add form validation
   - Integrar opciones de login social

2. **Authentication Logic**
   - Implement JWT authentication
   - Create user session management
   - Set up secure storage
   - Add remember me functionality
   - Implementar sistema de roles y permisos

3. **User Profile**
   - Design profile page
   - Implement settings management
   - Create avatar upload functionality
   - Add account management options
   - Integrar panel de progreso y logros del usuario

### Phase 4: Phrase Management (Weeks 7-9)

1. **Phrase Data Structure**
   - Define phrase schema
   - Implement data storage
   - Create data access layer
   - Set up synchronization
   - Diseñar estructura para categorías y etiquetas

2. **Phrase UI Components**
   - Design phrase cards with neumorphic styling
   - Create phrase editor
   - Implement category management
   - Add search and filtering
   - Optimizar diseño para ocupar mayoría de pantalla en móvil

3. **Phrase Functionality**
   - Implement CRUD operations
   - Add bulk actions
   - Create import/export functionality
   - Implement sharing features
   - Añadir sistema de favoritos y colecciones personalizadas

### Phase 5: Phonetic Features (Weeks 10-12)

1. **Phonetic Text Display**
   - Design phonetic text styling
   - Implement text rendering
   - Create interactive elements
   - Add pronunciation guides
   - Destacar visualmente la pronunciación fonética

2. **Speech Functionality**
   - Implement Web Speech API integration
   - Create audio playback controls
   - Add speech recognition
   - Implement pronunciation feedback
   - Desarrollar opciones de personalización de voz

3. **Practice Mode**
   - Design practice interface
   - Create scoring system
   - Implement progress tracking
   - Add gamification elements
   - Desarrollar sistema de retroalimentación visual en tiempo real

### Phase 6: Gamification System (Weeks 13-14)

1. **Achievement System**
   - Design achievement badges and medals
   - Implement achievement tracking
   - Create notification system
   - Add social sharing
   - Desarrollar sistema de niveles y progresión

2. **Streak and Rewards**
   - Implement daily streak tracking
   - Create reward system
   - Design progress visualization
   - Add milestone celebrations
   - Desarrollar sistema de recompensas por consistencia

3. **Leaderboards and Challenges**
   - Design leaderboard interface
   - Implement ranking system
   - Create daily and weekly challenges
   - Add friend referral system
   - Desarrollar competencias amistosas entre usuarios

### Phase 7: Quiz and Practice Modes (Weeks 15-16)

1. **Quiz Types**
   - Implement translation quiz
   - Create comprehension quiz
   - Design fill-in-the-blank exercises
   - Add listening exercises
   - Desarrollar sistema adaptativo de dificultad

2. **Practice Interface**
   - Design practice mode UI
   - Implement scoring system
   - Create feedback mechanisms
   - Add progress tracking
   - Integrar con sistema de logros

3. **Spaced Repetition**
   - Implement spaced repetition algorithm
   - Create review scheduling
   - Design progress indicators
   - Add performance analytics
   - Desarrollar sistema de repaso inteligente

### Phase 8: Video Management (Weeks 17-18)

1. **Video Data Structure**
   - Define video schema
   - Implement YouTube API integration
   - Create data access layer
   - Set up caching
   - Diseñar sistema de categorización por nivel

2. **Video UI Components**
   - Design video cards with neumorphic styling
   - Create video browser
   - Implement category navigation
   - Add search and filtering
   - Desarrollar sistema de recomendaciones

3. **Video Functionality**
   - Implement playback controls
   - Add favorites/bookmarks
   - Create playlist management
   - Implement offline viewing
   - Añadir transcripciones y ejercicios relacionados

### Phase 9: Books Section (Weeks 19-20)

1. **Books Library**
   - Design books interface
   - Implement PDF viewer
   - Create category organization
   - Add search functionality
   - Integrar enlaces de afiliados de Amazon

2. **Download System**
   - Implement download management
   - Create donation integration
   - Design download progress UI
   - Add offline access
   - Integrar con Buy Me a Coffee

3. **Reading Tools**
   - Implement bookmarking system
   - Create notes functionality
   - Design reading progress tracking
   - Add study guides
   - Desarrollar recomendaciones personalizadas

### Phase 10: Admin Panel (Weeks 21-22)

1. **Admin UI**
   - Design admin dashboard
   - Create user management interface
   - Implement content moderation tools
   - Add system settings
   - Diseñar panel de estadísticas y análisis

2. **Admin Functionality**
   - Implement role-based access control
   - Create analytics dashboard
   - Add bulk operations
   - Implement audit logging
   - Desarrollar herramientas de gestión de contenido

3. **AI Integration**
   - Implement Google AI for phrase generation
   - Create phrase suggestion system
   - Design AI-assisted content creation
   - Add automated translations
   - Desarrollar herramientas de análisis de contenido

### Phase 11: Offline Capabilities (Weeks 23-24)

1. **Service Worker**
   - Implement complete service worker
   - Create cache management
   - Add background sync
   - Implement push notifications
   - Optimizar para uso con conexión limitada

2. **Offline UI**
   - Design connection status indicator
   - Create offline mode interface
   - Implement sync management
   - Add conflict resolution UI
   - Desarrollar sistema de priorización de contenido

3. **Offline Data**
   - Implement IndexedDB storage
   - Create data synchronization
   - Add conflict resolution logic
   - Implement offline-first architecture
   - Optimizar almacenamiento para dispositivos móviles

### Phase 12: Testing and Optimization (Weeks 25-26)

1. **Testing**
   - Perform cross-browser testing
   - Test on various mobile devices
   - Conduct performance testing
   - Implement automated tests
   - Realizar pruebas de usabilidad con usuarios reales

2. **Optimization**
   - Optimize asset loading
   - Implement code splitting
   - Reduce bundle size
   - Improve rendering performance
   - Optimizar para dispositivos de gama baja

3. **Accessibility**
   - Ensure WCAG compliance
   - Implement keyboard navigation
   - Add screen reader support
   - Test with accessibility tools
   - Mejorar contraste y legibilidad

### Phase 13: Launch Preparation (Weeks 27-28)

1. **Documentation**
   - Create user documentation
   - Write technical documentation
   - Document API endpoints
   - Create maintenance guides
   - Desarrollar tutoriales interactivos

2. **Final Polishing**
   - Address feedback
   - Fix remaining issues
   - Add final animations
   - Conduct final review
   - Realizar optimizaciones finales de rendimiento

3. **Launch**
   - Deploy to production
   - Configure analytics
   - Set up monitoring
   - Prepare marketing materials
   - Implementar plan de lanzamiento por fases

## 6. CSS Implementation Guidelines

### 6.1 Neumorphic Component Styling

```css
/* Example of neumorphic card styling */
.neumorphic-card {
  background-color: var(--bs-tertiary-bg);
  border-radius: var(--bs-border-radius);
  border: none;
  box-shadow: 5px 5px 10px rgba(55, 94, 148, 0.2), -5px -5px 10px rgba(255, 255, 255, 0.4);
  transition: transform 0.2s, box-shadow 0.2s;
  padding: 1.5rem;
}

.neumorphic-card:hover {
  transform: translateY(-5px);
  box-shadow: 8px 8px 40px rgba(0, 0, 0, 0.15), -5px -5px 10px rgba(255, 255, 255, 0.4);
}

/* Example of neumorphic button styling */
.neumorphic-button {
  background-color: var(--bs-tertiary-bg);
  color: var(--bs-body-color);
  border: none;
  border-radius: var(--bs-border-radius-pill);
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  box-shadow: 5px 5px 10px rgba(55, 94, 148, 0.2), -5px -5px 10px rgba(255, 255, 255, 0.4);
  transition: all 0.3s ease;
}

.neumorphic-button:hover {
  transform: translateY(-2px);
}

.neumorphic-button:active {
  transform: translateY(0);
  box-shadow: inset 2px 2px 8px rgba(55, 94, 148, 0.3), inset -3px -2px 5px rgba(255, 255, 255, 0.8);
}

/* Example of neumorphic form input */
.neumorphic-input {
  background-color: var(--bs-tertiary-bg);
  border: none;
  border-radius: var(--bs-border-radius);
  padding: 0.75rem 1rem;
  box-shadow: inset 2px 2px 8px rgba(55, 94, 148, 0.3), inset -3px -2px 5px rgba(255, 255, 255, 0.8);
  transition: all 0.3s ease;
}

.neumorphic-input:focus {
  outline: none;
  box-shadow: inset 2px 2px 8px rgba(55, 94, 148, 0.3), inset -3px -2px 5px rgba(255, 255, 255, 0.8), 0 0 0 3px rgba(55, 141, 252, 0.25);
}

/* Example of neumorphic toggle/switch */
.neumorphic-switch {
  position: relative;
  display: inline-block;
  width: 60px;
  height: 34px;
}

.neumorphic-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.neumorphic-switch .slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--bs-tertiary-bg);
  border-radius: 34px;
  box-shadow: inset 2px 2px 8px rgba(55, 94, 148, 0.3), inset -3px -2px 5px rgba(255, 255, 255, 0.8);
  transition: .4s;
}

.neumorphic-switch .slider:before {
  position: absolute;
  content: "";
  height: 26px;
  width: 26px;
  left: 4px;
  bottom: 4px;
  background-color: white;
  border-radius: 50%;
  box-shadow: 2px 2px 5px rgba(0, 0, 0, 0.2);
  transition: .4s;
}

.neumorphic-switch input:checked + .slider {
  background-color: var(--bs-primary);
}

.neumorphic-switch input:checked + .slider:before {
  transform: translateX(26px);
}
```

### 6.2 Custom Component Guidelines

1. **Consistent Shadow Values**
   - Use explicit shadow values for critical components
   - Maintain the dual shadow approach (positive and negative)
   - Adjust shadow intensity based on component size

2. **Color Usage**
   - Use CSS variables for all colors
   - Maintain contrast ratios for accessibility
   - Use opacity for state changes rather than color changes

3. **Interaction States**
   - Hover: Slight elevation (translateY) and shadow increase
   - Active/Pressed: Inset shadow effect
   - Focus: Subtle outline with primary color

4. **Animation Guidelines**
   - Keep transitions between 0.2s and 0.3s
   - Use ease or ease-in-out timing functions
   - Animate both transform and shadow properties
   - Avoid animating too many properties simultaneously

## 7. JavaScript Architecture

### 7.1 Module Pattern

```javascript
// Example module pattern for phrase management
const PhrasesManager = (function() {
  // Private variables
  let phrases = [];
  let categories = [];
  let currentCategory = null;

  // Private methods
  const loadPhrases = async () => {
    try {
      // Load from IndexedDB first (offline-first approach)
      const localPhrases = await DataManager.getLocalPhrases();
      if (localPhrases.length > 0) {
        phrases = localPhrases;
        return phrases;
      }

      // If no local data, try to fetch from server
      if (navigator.onLine) {
        const remotePhrases = await DataManager.fetchPhrases();
        phrases = remotePhrases;
        // Store for offline use
        await DataManager.saveLocalPhrases(phrases);
        return phrases;
      }

      return [];
    } catch (error) {
      console.error('Error loading phrases:', error);
      return [];
    }
  };

  // Public API
  return {
    initialize: async function() {
      await loadPhrases();
      await this.loadCategories();
      this.renderPhrasesList();
      this.setupEventListeners();
    },

    loadCategories: async function() {
      categories = await DataManager.getCategories();
      return categories;
    },

    filterByCategory: function(categoryId) {
      currentCategory = categoryId;
      this.renderPhrasesList();
    },

    renderPhrasesList: function() {
      const container = document.getElementById('phrases-container');
      if (!container) return;

      // Clear container
      container.innerHTML = '';

      // Filter phrases if category is selected
      const filteredPhrases = currentCategory
        ? phrases.filter(p => p.categoryId === currentCategory)
        : phrases;

      // Render each phrase
      filteredPhrases.forEach(phrase => {
        const phraseCard = this.createPhraseCard(phrase);
        container.appendChild(phraseCard);
      });
    },

    createPhraseCard: function(phrase) {
      const card = document.createElement('div');
      card.className = 'phrase-card';
      card.dataset.id = phrase.id;

      card.innerHTML = `
        <div class="phrase-english">${phrase.english}</div>
        <div class="phrase-phonetic" onclick="WebSpeechService.speak('${phrase.english}')">${phrase.phonetic}</div>
        <div class="phrase-spanish">${phrase.spanish}</div>
        <div class="phrase-actions">
          <button class="btn-edit" aria-label="Edit"><i class="bi bi-pencil"></i></button>
          <button class="btn-delete" aria-label="Delete"><i class="bi bi-trash"></i></button>
        </div>
      `;

      // Add event listeners
      card.querySelector('.btn-edit').addEventListener('click', () => {
        this.editPhrase(phrase.id);
      });

      card.querySelector('.btn-delete').addEventListener('click', () => {
        this.deletePhrase(phrase.id);
      });

      return card;
    },

    addPhrase: async function(phraseData) {
      try {
        const newPhrase = await DataManager.addPhrase(phraseData);
        phrases.push(newPhrase);
        this.renderPhrasesList();
        return newPhrase;
      } catch (error) {
        console.error('Error adding phrase:', error);
        throw error;
      }
    },

    editPhrase: function(phraseId) {
      const phrase = phrases.find(p => p.id === phraseId);
      if (!phrase) return;

      // Show edit modal with phrase data
      const modal = document.getElementById('edit-phrase-modal');
      if (!modal) return;

      // Fill form fields
      modal.querySelector('#edit-english').value = phrase.english;
      modal.querySelector('#edit-phonetic').value = phrase.phonetic;
      modal.querySelector('#edit-spanish').value = phrase.spanish;
      modal.querySelector('#edit-category').value = phrase.categoryId || '';

      // Set phrase ID in form
      modal.querySelector('#edit-phrase-id').value = phraseId;

      // Show modal
      const bsModal = new bootstrap.Modal(modal);
      bsModal.show();
    },

    deletePhrase: async function(phraseId) {
      if (confirm('Are you sure you want to delete this phrase?')) {
        try {
          await DataManager.deletePhrase(phraseId);
          phrases = phrases.filter(p => p.id !== phraseId);
          this.renderPhrasesList();
        } catch (error) {
          console.error('Error deleting phrase:', error);
        }
      }
    },

    setupEventListeners: function() {
      // Add phrase form submission
      const addForm = document.getElementById('add-phrase-form');
      if (addForm) {
        addForm.addEventListener('submit', async (e) => {
          e.preventDefault();

          const phraseData = {
            english: addForm.querySelector('#add-english').value,
            phonetic: addForm.querySelector('#add-phonetic').value,
            spanish: addForm.querySelector('#add-spanish').value,
            categoryId: addForm.querySelector('#add-category').value || null
          };

          try {
            await this.addPhrase(phraseData);
            addForm.reset();
            // Close modal if using one
            const modal = bootstrap.Modal.getInstance(document.getElementById('add-phrase-modal'));
            if (modal) modal.hide();
          } catch (error) {
            alert('Error adding phrase. Please try again.');
          }
        });
      }

      // Edit phrase form submission
      const editForm = document.getElementById('edit-phrase-form');
      if (editForm) {
        editForm.addEventListener('submit', async (e) => {
          e.preventDefault();

          const phraseId = editForm.querySelector('#edit-phrase-id').value;
          const phraseData = {
            id: phraseId,
            english: editForm.querySelector('#edit-english').value,
            phonetic: editForm.querySelector('#edit-phonetic').value,
            spanish: editForm.querySelector('#edit-spanish').value,
            categoryId: editForm.querySelector('#edit-category').value || null
          };

          try {
            await DataManager.updatePhrase(phraseData);
            // Update local array
            const index = phrases.findIndex(p => p.id === phraseId);
            if (index !== -1) {
              phrases[index] = phraseData;
            }
            this.renderPhrasesList();

            // Close modal
            const modal = bootstrap.Modal.getInstance(document.getElementById('edit-phrase-modal'));
            if (modal) modal.hide();
          } catch (error) {
            console.error('Error updating phrase:', error);
            alert('Error updating phrase. Please try again.');
          }
        });
      }

      // Category filter
      const categorySelect = document.getElementById('category-filter');
      if (categorySelect) {
        categorySelect.addEventListener('change', (e) => {
          this.filterByCategory(e.target.value || null);
        });
      }
    }
  };
})();

### 7.2 Service Worker Implementation

```javascript
// sw.js - Service Worker for offline functionality

const CACHE_NAME = 'inglesylatinos-v1';

// Resources to cache initially
const INITIAL_CACHE_URLS = [
  './',
  './index.html',
  './offline.html',
  './css/bootstrap-morph.css',
  './css/theme-variables.css',
  './css/custom-components.css',
  './js/app.js',
  './js/data-manager.js',
  './js/phrases-manager.js',
  './js/web-speech-service.js',
  './js/auth-service.js',
  './js/video-service.js',
  './manifest.json',
  './img/logo/logo-192.png',
  './img/logo/logo-512.png',
  './img/icons/offline-icon.svg',
  'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css',
  'https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&display=swap'
];

// Install event - cache initial resources
self.addEventListener('install', event => {
  event.waitUntil(
    caches.open(CACHE_NAME)
      .then(cache => {
        console.log('Caching initial resources');
        return cache.addAll(INITIAL_CACHE_URLS);
      })
      .then(() => self.skipWaiting())
  );
});

// Activate event - clean up old caches
self.addEventListener('activate', event => {
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.filter(cacheName => {
          return cacheName !== CACHE_NAME;
        }).map(cacheName => {
          console.log('Deleting old cache:', cacheName);
          return caches.delete(cacheName);
        })
      );
    }).then(() => self.clients.claim())
  );
});

// Fetch event - serve from cache or network
self.addEventListener('fetch', event => {
  // Skip cross-origin requests
  if (!event.request.url.startsWith(self.location.origin) &&
      !event.request.url.includes('fonts.googleapis.com') &&
      !event.request.url.includes('cdn.jsdelivr.net')) {
    return;
  }

  // API requests - network first, then offline handling
  if (event.request.url.includes('/api/')) {
    return event.respondWith(
      fetch(event.request)
        .catch(() => {
          // If it's a GET request, try to return cached data
          if (event.request.method === 'GET') {
            return caches.match(event.request);
          }

          // For other methods (POST, PUT, DELETE), queue for later
          // and return a response indicating offline status
          return new Response(
            JSON.stringify({
              success: false,
              message: 'You are currently offline. This action will be processed when you reconnect.',
              offlineQueued: true
            }),
            {
              headers: { 'Content-Type': 'application/json' },
              status: 503
            }
          );
        })
    );
  }

  // HTML pages - network first, then cache, then offline page
  if (event.request.mode === 'navigate' ||
      (event.request.method === 'GET' &&
       event.request.headers.get('accept').includes('text/html'))) {

    event.respondWith(
      fetch(event.request)
        .then(response => {
          // Cache the latest version
          const responseClone = response.clone();
          caches.open(CACHE_NAME).then(cache => {
            cache.put(event.request, responseClone);
          });
          return response;
        })
        .catch(() => {
          // Try to get from cache
          return caches.match(event.request)
            .then(cachedResponse => {
              if (cachedResponse) {
                return cachedResponse;
              }
              // If not in cache, return offline page
              return caches.match('./offline.html');
            });
        })
    );
    return;
  }

  // Other assets - cache first, then network
  event.respondWith(
    caches.match(event.request)
      .then(cachedResponse => {
        // Return cached response if available
        if (cachedResponse) {
          return cachedResponse;
        }

        // Otherwise fetch from network
        return fetch(event.request)
          .then(response => {
            // Cache the new response
            const responseClone = response.clone();
            caches.open(CACHE_NAME).then(cache => {
              cache.put(event.request, responseClone);
            });
            return response;
          });
      })
  );
});

// Background sync for offline operations
self.addEventListener('sync', event => {
  if (event.tag === 'sync-phrases') {
    event.waitUntil(syncPhrases());
  } else if (event.tag === 'sync-user-data') {
    event.waitUntil(syncUserData());
  }
});

// Push notification handling
self.addEventListener('push', event => {
  const data = event.data.json();

  const options = {
    body: data.body,
    icon: './img/logo/logo-192.png',
    badge: './img/logo/badge.png',
    data: {
      url: data.url || '/'
    }
  };

  event.waitUntil(
    self.registration.showNotification(data.title, options)
  );
});

// Notification click handling
self.addEventListener('notificationclick', event => {
  event.notification.close();

  event.waitUntil(
    clients.matchAll({type: 'window'})
      .then(clientList => {
        // If a window is already open, focus it
        for (const client of clientList) {
          if (client.url === event.notification.data.url && 'focus' in client) {
            return client.focus();
          }
        }
        // Otherwise open a new window
        if (clients.openWindow) {
          return clients.openWindow(event.notification.data.url);
        }
      })
  );
});
```

## 8. Data Management

### 8.1 IndexedDB Structure

```javascript
// data-manager.js - Data management service

const DataManager = (function() {
  // Database name and version
  const DB_NAME = 'inglesylatinos-db';
  const DB_VERSION = 1;

  // Store names
  const STORES = {
    PHRASES: 'phrases',
    CATEGORIES: 'categories',
    VIDEOS: 'videos',
    USER_DATA: 'userData',
    PENDING_PHRASES: 'pendingPhrases',
    PENDING_VIDEOS: 'pendingVideos'
  };

  // Open database connection
  const openDB = () => {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_NAME, DB_VERSION);

      request.onerror = event => {
        console.error('Database error:', event.target.error);
        reject(event.target.error);
      };

      request.onsuccess = event => {
        resolve(event.target.result);
      };

      request.onupgradeneeded = event => {
        const db = event.target.result;

        // Create object stores if they don't exist
        if (!db.objectStoreNames.contains(STORES.PHRASES)) {
          db.createObjectStore(STORES.PHRASES, { keyPath: 'id' });
        }

        if (!db.objectStoreNames.contains(STORES.CATEGORIES)) {
          db.createObjectStore(STORES.CATEGORIES, { keyPath: 'id' });
        }

        if (!db.objectStoreNames.contains(STORES.VIDEOS)) {
          db.createObjectStore(STORES.VIDEOS, { keyPath: 'id' });
        }

        if (!db.objectStoreNames.contains(STORES.USER_DATA)) {
          db.createObjectStore(STORES.USER_DATA, { keyPath: 'key' });
        }

        if (!db.objectStoreNames.contains(STORES.PENDING_PHRASES)) {
          db.createObjectStore(STORES.PENDING_PHRASES, { keyPath: 'id', autoIncrement: true });
        }

        if (!db.objectStoreNames.contains(STORES.PENDING_VIDEOS)) {
          db.createObjectStore(STORES.PENDING_VIDEOS, { keyPath: 'id', autoIncrement: true });
        }
      };
    });
  };

  // Generic database operations
  const dbGet = (storeName, key) => {
    return new Promise(async (resolve, reject) => {
      try {
        const db = await openDB();
        const transaction = db.transaction(storeName, 'readonly');
        const store = transaction.objectStore(storeName);
        const request = store.get(key);

        request.onsuccess = () => {
          resolve(request.result);
        };

        request.onerror = () => {
          reject(request.error);
        };
      } catch (error) {
        reject(error);
      }
    });
  };

  const dbGetAll = (storeName) => {
    return new Promise(async (resolve, reject) => {
      try {
        const db = await openDB();
        const transaction = db.transaction(storeName, 'readonly');
        const store = transaction.objectStore(storeName);
        const request = store.getAll();

        request.onsuccess = () => {
          resolve(request.result);
        };

        request.onerror = () => {
          reject(request.error);
        };
      } catch (error) {
        reject(error);
      }
    });
  };

  const dbAdd = (storeName, data) => {
    return new Promise(async (resolve, reject) => {
      try {
        const db = await openDB();
        const transaction = db.transaction(storeName, 'readwrite');
        const store = transaction.objectStore(storeName);
        const request = store.add(data);

        request.onsuccess = () => {
          resolve(data);
        };

        request.onerror = () => {
          reject(request.error);
        };
      } catch (error) {
        reject(error);
      }
    });
  };

  const dbPut = (storeName, data) => {
    return new Promise(async (resolve, reject) => {
      try {
        const db = await openDB();
        const transaction = db.transaction(storeName, 'readwrite');
        const store = transaction.objectStore(storeName);
        const request = store.put(data);

        request.onsuccess = () => {
          resolve(data);
        };

        request.onerror = () => {
          reject(request.error);
        };
      } catch (error) {
        reject(error);
      }
    });
  };

  const dbDelete = (storeName, key) => {
    return new Promise(async (resolve, reject) => {
      try {
        const db = await openDB();
        const transaction = db.transaction(storeName, 'readwrite');
        const store = transaction.objectStore(storeName);
        const request = store.delete(key);

        request.onsuccess = () => {
          resolve(true);
        };

        request.onerror = () => {
          reject(request.error);
        };
      } catch (error) {
        reject(error);
      }
    });
  };

  const dbClear = (storeName) => {
    return new Promise(async (resolve, reject) => {
      try {
        const db = await openDB();
        const transaction = db.transaction(storeName, 'readwrite');
        const store = transaction.objectStore(storeName);
        const request = store.clear();

        request.onsuccess = () => {
          resolve(true);
        };

        request.onerror = () => {
          reject(request.error);
        };
      } catch (error) {
        reject(error);
      }
    });
  };

  // Public API
  return {
    // Phrase operations
    getPhrase: async (id) => {
      return await dbGet(STORES.PHRASES, id);
    },

    getAllPhrases: async () => {
      return await dbGetAll(STORES.PHRASES);
    },

    addPhrase: async (phrase) => {
      // Generate ID if not provided
      if (!phrase.id) {
        phrase.id = crypto.randomUUID();
      }

      // Add timestamp
      phrase.createdAt = new Date().toISOString();
      phrase.updatedAt = phrase.createdAt;

      try {
        // Try to save to server first if online
        if (navigator.onLine) {
          const token = AuthService.getToken();
          const response = await fetch('/api/phrases', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(phrase)
          });

          if (response.ok) {
            const serverPhrase = await response.json();
            // Save to local DB
            await dbPut(STORES.PHRASES, serverPhrase);
            return serverPhrase;
          } else {
            throw new Error('Server error');
          }
        } else {
          // If offline, save locally and queue for sync
          await dbPut(STORES.PHRASES, phrase);

          // Add to pending queue
          await dbAdd(STORES.PENDING_PHRASES, {
            method: 'POST',
            data: phrase,
            token: AuthService.getToken(),
            timestamp: new Date().toISOString()
          });

          // Register for background sync if available
          if ('serviceWorker' in navigator && 'SyncManager' in window) {
            const registration = await navigator.serviceWorker.ready;
            await registration.sync.register('sync-phrases');
          }

          return phrase;
        }
      } catch (error) {
        // If any error occurs, save locally
        await dbPut(STORES.PHRASES, phrase);

        // Add to pending queue
        await dbAdd(STORES.PENDING_PHRASES, {
          method: 'POST',
          data: phrase,
          token: AuthService.getToken(),
          timestamp: new Date().toISOString()
        });

        return phrase;
      }
    },

    // Similar methods for updatePhrase, deletePhrase, etc.

    // Category operations
    getCategory: async (id) => {
      return await dbGet(STORES.CATEGORIES, id);
    },

    getAllCategories: async () => {
      return await dbGetAll(STORES.CATEGORIES);
    },

    // Video operations
    getVideo: async (id) => {
      return await dbGet(STORES.VIDEOS, id);
    },

    getAllVideos: async () => {
      return await dbGetAll(STORES.VIDEOS);
    },

    // User data operations
    getUserData: async (key) => {
      return await dbGet(STORES.USER_DATA, key);
    },

    setUserData: async (key, value) => {
      return await dbPut(STORES.USER_DATA, { key, value });
    },

    // Sync operations
    getPendingOperations: async () => {
      const pendingPhrases = await dbGetAll(STORES.PENDING_PHRASES);
      const pendingVideos = await dbGetAll(STORES.PENDING_VIDEOS);

      return {
        phrases: pendingPhrases,
        videos: pendingVideos,
        total: pendingPhrases.length + pendingVideos.length
      };
    },

    clearPendingOperations: async () => {
      await dbClear(STORES.PENDING_PHRASES);
      await dbClear(STORES.PENDING_VIDEOS);
      return true;
    }
  };
})();

### 8.2 API Integration

```javascript
// api-service.js - API integration service

const ApiService = (function() {
  // Base API URL
  const API_BASE_URL = '/api';

  // Default headers
  const getHeaders = () => {
    const headers = {
      'Content-Type': 'application/json'
    };

    // Add auth token if available
    const token = AuthService.getToken();
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    return headers;
  };

  // Generic API request
  const apiRequest = async (endpoint, method = 'GET', data = null) => {
    const url = `${API_BASE_URL}${endpoint}`;

    const options = {
      method,
      headers: getHeaders()
    };

    if (data && (method === 'POST' || method === 'PUT')) {
      options.body = JSON.stringify(data);
    }

    try {
      const response = await fetch(url, options);

      // Handle unauthorized
      if (response.status === 401) {
        AuthService.logout();
        window.location.href = '/login.html';
        throw new Error('Unauthorized');
      }

      // Parse JSON response
      const result = await response.json();

      // Handle API errors
      if (!response.ok) {
        throw new Error(result.message || 'API Error');
      }

      return result;
    } catch (error) {
      console.error('API Error:', error);
      throw error;
    }
  };

  // Public API
  return {
    // Auth endpoints
    login: async (credentials) => {
      return await apiRequest('/auth/login', 'POST', credentials);
    },

    register: async (userData) => {
      return await apiRequest('/auth/register', 'POST', userData);
    },

    // Phrases endpoints
    getPhrases: async () => {
      return await apiRequest('/phrases');
    },

    getPhrase: async (id) => {
      return await apiRequest(`/phrases/${id}`);
    },

    createPhrase: async (phraseData) => {
      return await apiRequest('/phrases', 'POST', phraseData);
    },

    updatePhrase: async (id, phraseData) => {
      return await apiRequest(`/phrases/${id}`, 'PUT', phraseData);
    },

    deletePhrase: async (id) => {
      return await apiRequest(`/phrases/${id}`, 'DELETE');
    },

    // Categories endpoints
    getCategories: async () => {
      return await apiRequest('/categories');
    },

    // Videos endpoints
    getVideos: async () => {
      return await apiRequest('/videos');
    },

    getVideosByCategory: async (categoryId) => {
      return await apiRequest(`/videos?category=${categoryId}`);
    },

    // User endpoints
    getUserProfile: async () => {
      return await apiRequest('/user/profile');
    },

    updateUserProfile: async (profileData) => {
      return await apiRequest('/user/profile', 'PUT', profileData);
    },

    // Admin endpoints
    getUsers: async () => {
      return await apiRequest('/admin/users');
    },

    getStats: async () => {
      return await apiRequest('/admin/stats');
    }
  };
})();
```

## 9. Authentication System

### 9.1 Authentication Service

```javascript
// auth-service.js - Authentication service

const AuthService = (function() {
  // Storage keys
  const TOKEN_KEY = 'auth_token';
  const USER_KEY = 'auth_user';

  // Event for auth state changes
  const authStateChanged = new CustomEvent('authStateChanged');

  // Get token from storage
  const getToken = () => {
    return localStorage.getItem(TOKEN_KEY);
  };

  // Set token in storage
  const setToken = (token) => {
    localStorage.setItem(TOKEN_KEY, token);
  };

  // Remove token from storage
  const removeToken = () => {
    localStorage.removeItem(TOKEN_KEY);
  };

  // Get user from storage
  const getUser = () => {
    const userJson = localStorage.getItem(USER_KEY);
    return userJson ? JSON.parse(userJson) : null;
  };

  // Set user in storage
  const setUser = (user) => {
    localStorage.setItem(USER_KEY, JSON.stringify(user));
  };

  // Remove user from storage
  const removeUser = () => {
    localStorage.removeItem(USER_KEY);
  };

  // Check if token is expired
  const isTokenExpired = (token) => {
    if (!token) return true;

    try {
      // Get payload from token
      const payload = JSON.parse(atob(token.split('.')[1]));

      // Check if token is expired
      return payload.exp < Date.now() / 1000;
    } catch (error) {
      return true;
    }
  };

  // Public API
  return {
    // Initialize auth state
    init: () => {
      const token = getToken();

      // Check if token exists and is not expired
      if (token && !isTokenExpired(token)) {
        // Token is valid, dispatch event
        document.dispatchEvent(authStateChanged);
        return true;
      } else {
        // Token is invalid or expired, clear auth state
        removeToken();
        removeUser();
        return false;
      }
    },

    // Login user
    login: async (email, password) => {
      try {
        const response = await fetch('/api/auth/login', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ email, password })
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.message || 'Login failed');
        }

        const data = await response.json();

        // Save token and user data
        setToken(data.token);
        setUser(data.user);

        // Dispatch auth state changed event
        document.dispatchEvent(authStateChanged);

        return data.user;
      } catch (error) {
        console.error('Login error:', error);
        throw error;
      }
    },

    // Register user
    register: async (userData) => {
      try {
        const response = await fetch('/api/auth/register', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify(userData)
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.message || 'Registration failed');
        }

        const data = await response.json();

        // Save token and user data
        setToken(data.token);
        setUser(data.user);

        // Dispatch auth state changed event
        document.dispatchEvent(authStateChanged);

        return data.user;
      } catch (error) {
        console.error('Registration error:', error);
        throw error;
      }
    },

    // Logout user
    logout: () => {
      // Clear auth state
      removeToken();
      removeUser();

      // Dispatch auth state changed event
      document.dispatchEvent(authStateChanged);
    },

    // Check if user is authenticated
    isAuthenticated: () => {
      const token = getToken();
      return token && !isTokenExpired(token);
    },

    // Get current user
    getCurrentUser: () => {
      return getUser();
    },

    // Check if user has role
    hasRole: (role) => {
      const user = getUser();
      return user && user.roles && user.roles.includes(role);
    },

    // Get token
    getToken: () => {
      return getToken();
    },

    // Update user profile
    updateProfile: async (profileData) => {
      try {
        const response = await fetch('/api/user/profile', {
          method: 'PUT',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${getToken()}`
          },
          body: JSON.stringify(profileData)
        });

        if (!response.ok) {
          const error = await response.json();
          throw new Error(error.message || 'Profile update failed');
        }

        const data = await response.json();

        // Update user data in storage
        const currentUser = getUser();
        setUser({ ...currentUser, ...data.user });

        return data.user;
      } catch (error) {
        console.error('Profile update error:', error);
        throw error;
      }
    }
  };
})();
```

### 9.2 Login and Registration Forms

```html
<!-- Login Form -->
<div class="auth-form-container">
  <form id="login-form" class="auth-form">
    <h2 class="auth-title">Iniciar Sesión</h2>

    <div class="form-group mb-3">
      <label for="login-email" class="form-label">Correo Electrónico</label>
      <input type="email" id="login-email" class="form-control neumorphic-input" required>
    </div>

    <div class="form-group mb-3">
      <label for="login-password" class="form-label">Contraseña</label>
      <input type="password" id="login-password" class="form-control neumorphic-input" required>
    </div>

    <div class="form-check mb-3">
      <input type="checkbox" id="remember-me" class="form-check-input">
      <label for="remember-me" class="form-check-label">Recordarme</label>
    </div>

    <div class="auth-actions">
      <button type="submit" class="btn btn-primary neumorphic-button w-100">Iniciar Sesión</button>
    </div>

    <div class="auth-links mt-3">
      <a href="#forgot-password" class="auth-link">¿Olvidaste tu contraseña?</a>
      <a href="#register" class="auth-link">Crear una cuenta</a>
    </div>
  </form>
</div>

<!-- Registration Form -->
<div class="auth-form-container">
  <form id="register-form" class="auth-form">
    <h2 class="auth-title">Crear Cuenta</h2>

    <div class="form-group mb-3">
      <label for="register-name" class="form-label">Nombre Completo</label>
      <input type="text" id="register-name" class="form-control neumorphic-input" required>
    </div>

    <div class="form-group mb-3">
      <label for="register-email" class="form-label">Correo Electrónico</label>
      <input type="email" id="register-email" class="form-control neumorphic-input" required>
    </div>

    <div class="form-group mb-3">
      <label for="register-password" class="form-label">Contraseña</label>
      <input type="password" id="register-password" class="form-control neumorphic-input" required>
    </div>

    <div class="form-group mb-3">
      <label for="register-confirm-password" class="form-label">Confirmar Contraseña</label>
      <input type="password" id="register-confirm-password" class="form-control neumorphic-input" required>
    </div>

    <div class="form-check mb-3">
      <input type="checkbox" id="terms-agreement" class="form-check-input" required>
      <label for="terms-agreement" class="form-check-label">Acepto los <a href="#terms">términos y condiciones</a></label>
    </div>

    <div class="auth-actions">
      <button type="submit" class="btn btn-primary neumorphic-button w-100">Crear Cuenta</button>
    </div>

    <div class="auth-links mt-3">
      <a href="#login" class="auth-link">¿Ya tienes una cuenta? Inicia sesión</a>
    </div>
  </form>
</div>
```

## 10. Phrase Management Implementation

### 10.1 Phrase Data Structure

```javascript
// Phrase data structure
const phraseSchema = {
  id: String,           // Unique identifier
  english: String,      // English text
  phonetic: String,     // Phonetic pronunciation
  spanish: String,      // Spanish translation
  categoryId: String,   // Category reference
  difficulty: Number,   // Difficulty level (1-5)
  tags: Array,          // Array of tags
  createdAt: String,    // Creation timestamp
  updatedAt: String,    // Last update timestamp
  userId: String,       // Creator reference
  favorite: Boolean,    // User favorite status
  notes: String         // User notes
};

// Category data structure
const categorySchema = {
  id: String,           // Unique identifier
  name: String,         // Category name
  description: String,  // Category description
  icon: String,         // Icon identifier
  parentId: String,     // Parent category (for subcategories)
  order: Number,        // Display order
  createdAt: String,    // Creation timestamp
  updatedAt: String     // Last update timestamp
};
```

### 10.2 Phrase UI Components

```html
<!-- Phrase Card Component -->
<div class="phrase-card" data-id="${phrase.id}">
  <div class="phrase-header">
    <span class="phrase-category">${categoryName}</span>
    <div class="phrase-actions">
      <button class="btn-favorite" aria-label="Favorite">
        <i class="bi ${phrase.favorite ? 'bi-star-fill' : 'bi-star'}"></i>
      </button>
      <button class="btn-edit" aria-label="Edit">
        <i class="bi bi-pencil"></i>
      </button>
      <button class="btn-delete" aria-label="Delete">
        <i class="bi bi-trash"></i>
      </button>
    </div>
  </div>

  <div class="phrase-content">
    <div class="phrase-english">${phrase.english}</div>
    <div class="phrase-phonetic" onclick="WebSpeechService.speak('${phrase.english}')">
      ${phrase.phonetic}
      <i class="bi bi-volume-up"></i>
    </div>
    <div class="phrase-spanish">${phrase.spanish}</div>
  </div>

  <div class="phrase-footer">
    <div class="phrase-difficulty">
      ${'<i class="bi bi-star-fill"></i>'.repeat(phrase.difficulty)}
      ${'<i class="bi bi-star"></i>'.repeat(5 - phrase.difficulty)}
    </div>
    <div class="phrase-tags">
      ${phrase.tags.map(tag => `<span class="phrase-tag">${tag}</span>`).join('')}
    </div>
  </div>
</div>

<!-- Phrase Form Component -->
<form id="phrase-form" class="phrase-form">
  <div class="form-group mb-3">
    <label for="phrase-english" class="form-label">English Text</label>
    <input type="text" id="phrase-english" class="form-control neumorphic-input" required>
  </div>

  <div class="form-group mb-3">
    <label for="phrase-phonetic" class="form-label">Phonetic Pronunciation</label>
    <input type="text" id="phrase-phonetic" class="form-control neumorphic-input" required>
    <button type="button" id="generate-phonetic" class="btn btn-sm btn-secondary mt-1">
      Generate Phonetic
    </button>
  </div>

  <div class="form-group mb-3">
    <label for="phrase-spanish" class="form-label">Spanish Translation</label>
    <input type="text" id="phrase-spanish" class="form-control neumorphic-input" required>
  </div>

  <div class="form-group mb-3">
    <label for="phrase-category" class="form-label">Category</label>
    <select id="phrase-category" class="form-select neumorphic-input">
      <option value="">-- Select Category --</option>
      ${categories.map(category => `<option value="${category.id}">${category.name}</option>`).join('')}
    </select>
  </div>

  <div class="form-group mb-3">
    <label for="phrase-difficulty" class="form-label">Difficulty Level</label>
    <div class="difficulty-selector">
      <input type="range" id="phrase-difficulty" class="form-range" min="1" max="5" value="3">
      <div class="difficulty-labels">
        <span>Easy</span>
        <span>Medium</span>
        <span>Hard</span>
      </div>
    </div>
  </div>

  <div class="form-group mb-3">
    <label for="phrase-tags" class="form-label">Tags (comma separated)</label>
    <input type="text" id="phrase-tags" class="form-control neumorphic-input">
  </div>

  <div class="form-group mb-3">
    <label for="phrase-notes" class="form-label">Notes</label>
    <textarea id="phrase-notes" class="form-control neumorphic-input" rows="3"></textarea>
  </div>

  <div class="form-actions">
    <button type="submit" class="btn btn-primary neumorphic-button">Save Phrase</button>
    <button type="button" class="btn btn-secondary neumorphic-button" data-bs-dismiss="modal">Cancel</button>
  </div>
</form>
```

## 11. Phonetic Features Implementation

### 11.1 Web Speech API Integration

```javascript
// web-speech-service.js - Speech synthesis and recognition service

const WebSpeechService = (function() {
  // Check if browser supports speech synthesis
  const synthSupported = 'speechSynthesis' in window;

  // Check if browser supports speech recognition
  const recognitionSupported = 'SpeechRecognition' in window || 'webkitSpeechRecognition' in window;

  // Initialize speech synthesis
  let synth = null;
  let voices = [];

  // Initialize speech recognition
  let recognition = null;

  // Initialize speech synthesis
  const initSynthesis = () => {
    if (!synthSupported) return false;

    synth = window.speechSynthesis;

    // Load voices
    voices = synth.getVoices();

    // Chrome loads voices asynchronously
    if (voices.length === 0) {
      synth.addEventListener('voiceschanged', () => {
        voices = synth.getVoices();
      });
    }

    return true;
  };

  // Initialize speech recognition
  const initRecognition = () => {
    if (!recognitionSupported) return false;

    // Create recognition object
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
    recognition = new SpeechRecognition();

    // Configure recognition
    recognition.continuous = false;
    recognition.interimResults = false;

    return true;
  };

  // Get English voice
  const getEnglishVoice = () => {
    // Try to find a US English voice
    let voice = voices.find(v => v.lang === 'en-US' && v.localService);

    // If not found, try any English voice
    if (!voice) {
      voice = voices.find(v => v.lang.startsWith('en-'));
    }

    // If still not found, use the first available voice
    if (!voice && voices.length > 0) {
      voice = voices[0];
    }

    return voice;
  };

  // Public API
  return {
    // Initialize the service
    init: function() {
      const synthInitialized = initSynthesis();
      const recognitionInitialized = initRecognition();

      return {
        synthSupported: synthInitialized,
        recognitionSupported: recognitionInitialized
      };
    },

    // Speak text
    speak: function(text, options = {}) {
      if (!synth) return false;

      // Cancel any ongoing speech
      synth.cancel();

      // Create utterance
      const utterance = new SpeechSynthesisUtterance(text);

      // Set voice
      utterance.voice = options.voice || getEnglishVoice();

      // Set other options
      utterance.rate = options.rate || 0.9;
      utterance.pitch = options.pitch || 1;
      utterance.volume = options.volume || 1;

      // Set event handlers
      if (options.onStart) utterance.onstart = options.onStart;
      if (options.onEnd) utterance.onend = options.onEnd;
      if (options.onError) utterance.onerror = options.onError;

      // Speak
      synth.speak(utterance);

      return true;
    },

    // Stop speaking
    stop: function() {
      if (!synth) return false;

      synth.cancel();
      return true;
    },

    // Start speech recognition
    startListening: function(options = {}) {
      if (!recognition) return false;

      // Set language
      recognition.lang = options.lang || 'en-US';

      // Set event handlers
      recognition.onresult = (event) => {
        const result = event.results[0][0].transcript;
        if (options.onResult) options.onResult(result, event.results[0][0].confidence);
      };

      if (options.onStart) recognition.onstart = options.onStart;
      if (options.onEnd) recognition.onend = options.onEnd;
      if (options.onError) recognition.onerror = options.onError;

      // Start listening
      recognition.start();

      return true;
    },

    // Stop speech recognition
    stopListening: function() {
      if (!recognition) return false;

      recognition.stop();
      return true;
    },

    // Get available voices
    getVoices: function() {
      return voices;
    },

    // Check if speech synthesis is speaking
    isSpeaking: function() {
      return synth ? synth.speaking : false;
    },

    // Generate phonetic representation
    generatePhonetic: function(text) {
      // This is a simple implementation
      // In a real app, you would use a more sophisticated algorithm
      // or an API for phonetic transcription

      const phonetics = {
        'a': 'a', 'e': 'i', 'i': 'ai', 'o': 'o', 'u': 'u',
        'ai': 'ei', 'ay': 'ei', 'ea': 'i', 'ee': 'i', 'oo': 'u',
        'th': 'z', 'ch': 'ch', 'sh': 'sh', 'ph': 'f',
        'j': 'dch', 'c': 'k', 'q': 'k', 'x': 'ks',
        'w': 'u', 'y': 'i'
      };

      let phonetic = text.toLowerCase();

      // Replace phonetic patterns
      Object.keys(phonetics).forEach(key => {
        phonetic = phonetic.replace(new RegExp(key, 'g'), phonetics[key]);
      });

      return phonetic;
    }
  };
})();
```

### 11.2 Phonetic Text Styling

```css
/* Phonetic text styling */
.phonetic {
  font-size: 1.375rem;
  color: white;
  font-weight: 500;
  margin-bottom: 1rem;
  transition: all 0.3s;
  line-height: 1.4;
  background-color: var(--bs-primary);
  padding: 0.5rem 1rem 0.5rem 2.5rem;
  border-radius: var(--bs-border-radius-pill);
  display: inline-block;
  box-shadow: 5px 5px 10px rgba(55, 94, 148, 0.2), -5px -5px 10px rgba(255, 255, 255, 0.4);
  cursor: pointer;
  position: relative;
}

.phonetic::before {
  content: "\F4B2"; /* Bootstrap Icons: volume-up */
  font-family: "bootstrap-icons";
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.2rem;
}

.phonetic:hover {
  transform: translateY(-2px);
  box-shadow: 8px 8px 40px rgba(0, 0, 0, 0.15);
}

.phonetic:active {
  transform: translateY(0);
  box-shadow: inset 2px 2px 8px rgba(55, 94, 148, 0.3), inset -3px -2px 5px rgba(255, 255, 255, 0.8);
}

/* Phonetic practice mode */
.practice-container {
  background-color: var(--bs-tertiary-bg);
  border-radius: var(--bs-border-radius);
  box-shadow: 5px 5px 10px rgba(55, 94, 148, 0.2), -5px -5px 10px rgba(255, 255, 255, 0.4);
  padding: 2rem;
  margin-bottom: 2rem;
}

.practice-phrase {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  text-align: center;
}

.practice-controls {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.practice-result {
  margin-top: 1.5rem;
  padding: 1rem;
  border-radius: var(--bs-border-radius);
  text-align: center;
  transition: all 0.3s;
}

.practice-result.correct {
  background-color: rgba(67, 204, 41, 0.1);
  color: var(--bs-success);
}

.practice-result.incorrect {
  background-color: rgba(229, 37, 39, 0.1);
  color: var(--bs-danger);
}

.practice-score {
  text-align: center;
  font-size: 1.25rem;
  margin-top: 1.5rem;
}

.practice-progress {
  height: 0.5rem;
  margin-top: 1rem;
  background-color: var(--bs-tertiary-bg);
  box-shadow: inset 2px 2px 8px rgba(55, 94, 148, 0.3), inset -3px -2px 5px rgba(255, 255, 255, 0.8);
}

.practice-progress .progress-bar {
  background-color: var(--bs-primary);
  box-shadow: none;
}
```

## 12. Video Management Implementation

### 12.1 YouTube API Integration

```javascript
// video-service.js - YouTube API integration service

const VideoService = (function() {
  // YouTube API key
  const API_KEY = 'YOUR_YOUTUBE_API_KEY';

  // YouTube API base URL
  const API_BASE_URL = 'https://www.googleapis.com/youtube/v3';

  // Get video details from YouTube API
  const getVideoDetails = async (videoId) => {
    try {
      const response = await fetch(`${API_BASE_URL}/videos?part=snippet,contentDetails,statistics&id=${videoId}&key=${API_KEY}`);

      if (!response.ok) {
        throw new Error('Failed to fetch video details');
      }

      const data = await response.json();

      if (data.items && data.items.length > 0) {
        const videoData = data.items[0];

        return {
          id: videoData.id,
          title: videoData.snippet.title,
          description: videoData.snippet.description,
          thumbnail: videoData.snippet.thumbnails.high.url,
          channelTitle: videoData.snippet.channelTitle,
          publishedAt: videoData.snippet.publishedAt,
          duration: videoData.contentDetails.duration,
          viewCount: videoData.statistics.viewCount,
          likeCount: videoData.statistics.likeCount
        };
      } else {
        throw new Error('Video not found');
      }
    } catch (error) {
      console.error('Error fetching video details:', error);
      throw error;
    }
  };

  // Extract video ID from YouTube URL
  const extractVideoId = (url) => {
    // Handle different YouTube URL formats
    const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/;
    const match = url.match(regExp);

    return (match && match[2].length === 11) ? match[2] : null;
  };

  // Format duration from ISO 8601 format
  const formatDuration = (isoDuration) => {
    const match = isoDuration.match(/PT(\d+H)?(\d+M)?(\d+S)?/);

    const hours = (match[1] && match[1].replace('H', '')) || 0;
    const minutes = (match[2] && match[2].replace('M', '')) || 0;
    const seconds = (match[3] && match[3].replace('S', '')) || 0;

    let formattedDuration = '';

    if (hours > 0) {
      formattedDuration += `${hours}:`;
      formattedDuration += `${minutes.toString().padStart(2, '0')}:`;
    } else {
      formattedDuration += `${minutes}:`;
    }

    formattedDuration += seconds.toString().padStart(2, '0');

    return formattedDuration;
  };

  // Public API
  return {
    // Get video details from URL
    getVideoFromUrl: async function(url) {
      const videoId = extractVideoId(url);

      if (!videoId) {
        throw new Error('Invalid YouTube URL');
      }

      const videoDetails = await getVideoDetails(videoId);

      // Format duration
      videoDetails.formattedDuration = formatDuration(videoDetails.duration);

      return videoDetails;
    },

    // Get embed URL for video
    getEmbedUrl: function(videoId, options = {}) {
      let embedUrl = `https://www.youtube.com/embed/${videoId}?rel=0`;

      // Add autoplay option
      if (options.autoplay) {
        embedUrl += '&autoplay=1';
      }

      // Add start time
      if (options.startTime) {
        embedUrl += `&start=${options.startTime}`;
      }

      // Add end time
      if (options.endTime) {
        embedUrl += `&end=${options.endTime}`;
      }

      // Add controls option
      if (options.controls === false) {
        embedUrl += '&controls=0';
      }

      return embedUrl;
    },

    // Search YouTube videos
    searchVideos: async function(query, maxResults = 10) {
      try {
        const response = await fetch(`${API_BASE_URL}/search?part=snippet&q=${encodeURIComponent(query)}&maxResults=${maxResults}&type=video&key=${API_KEY}`);

        if (!response.ok) {
          throw new Error('Failed to search videos');
        }

        const data = await response.json();

        return data.items.map(item => ({
          id: item.id.videoId,
          title: item.snippet.title,
          description: item.snippet.description,
          thumbnail: item.snippet.thumbnails.high.url,
          channelTitle: item.snippet.channelTitle,
          publishedAt: item.snippet.publishedAt
        }));
      } catch (error) {
        console.error('Error searching videos:', error);
        throw error;
      }
    },

    // Create video object for storage
    createVideoObject: function(videoDetails, categoryId = null, notes = '') {
      return {
        id: crypto.randomUUID(),
        videoId: videoDetails.id,
        title: videoDetails.title,
        description: videoDetails.description,
        thumbnail: videoDetails.thumbnail,
        channelTitle: videoDetails.channelTitle,
        duration: videoDetails.duration,
        formattedDuration: videoDetails.formattedDuration,
        categoryId: categoryId,
        notes: notes,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
    }
  };
})();
```

### 12.2 Video UI Components

```html
<!-- Video Card Component -->
<div class="video-card" data-id="${video.id}">
  <div class="video-thumbnail">
    <img src="${video.thumbnail}" alt="${video.title}" loading="lazy">
    <div class="video-duration">${video.formattedDuration}</div>
    <div class="video-play-button" data-video-id="${video.videoId}">
      <i class="bi bi-play-fill"></i>
    </div>
  </div>

  <div class="video-info">
    <h3 class="video-title">${video.title}</h3>
    <div class="video-channel">${video.channelTitle}</div>
    <div class="video-category">${categoryName}</div>
  </div>

  <div class="video-actions">
    <button class="btn-edit" aria-label="Edit">
      <i class="bi bi-pencil"></i>
    </button>
    <button class="btn-delete" aria-label="Delete">
      <i class="bi bi-trash"></i>
    </button>
  </div>
</div>

<!-- Video Player Component -->
<div class="video-player-container">
  <div class="video-player">
    <iframe
      src="${VideoService.getEmbedUrl(video.videoId)}"
      title="${video.title}"
      frameborder="0"
      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
      allowfullscreen>
    </iframe>
  </div>

  <div class="video-details">
    <h2 class="video-title">${video.title}</h2>
    <div class="video-meta">
      <span class="video-channel">${video.channelTitle}</span>
      <span class="video-duration">${video.formattedDuration}</span>
    </div>
    <div class="video-description">${video.description}</div>
    <div class="video-notes">
      <h4>Notes</h4>
      <p>${video.notes || 'No notes added yet.'}</p>
      <button class="btn btn-sm btn-secondary edit-notes-btn">
        <i class="bi bi-pencil"></i> Edit Notes
      </button>
    </div>
  </div>
</div>

<!-- Video Form Component -->
<form id="video-form" class="video-form">
  <div class="form-group mb-3">
    <label for="video-url" class="form-label">YouTube Video URL</label>
    <div class="input-group">
      <input type="text" id="video-url" class="form-control neumorphic-input" required>
      <button type="button" id="fetch-video-btn" class="btn btn-secondary">
        <i class="bi bi-arrow-repeat"></i> Fetch
      </button>
    </div>
  </div>

  <div id="video-preview" class="video-preview mb-3 d-none">
    <div class="video-preview-thumbnail">
      <img id="video-thumbnail" src="" alt="Video thumbnail">
      <div id="video-duration" class="video-duration"></div>
    </div>
    <div class="video-preview-info">
      <h4 id="video-title" class="video-preview-title"></h4>
      <div id="video-channel" class="video-preview-channel"></div>
    </div>
  </div>

  <div class="form-group mb-3">
    <label for="video-category" class="form-label">Category</label>
    <select id="video-category" class="form-select neumorphic-input">
      <option value="">-- Select Category --</option>
      ${categories.map(category => `<option value="${category.id}">${category.name}</option>`).join('')}
    </select>
  </div>

  <div class="form-group mb-3">
    <label for="video-notes" class="form-label">Notes</label>
    <textarea id="video-notes" class="form-control neumorphic-input" rows="3"></textarea>
  </div>

  <div class="form-actions">
    <button type="submit" class="btn btn-primary neumorphic-button">Save Video</button>
    <button type="button" class="btn btn-secondary neumorphic-button" data-bs-dismiss="modal">Cancel</button>
  </div>
</form>
```

## 13. Admin Panel Implementation

### 13.1 Admin Dashboard

```html
<!-- Admin Dashboard -->
<div class="admin-dashboard">
  <div class="row g-4">
    <!-- Stats Cards -->
    <div class="col-md-3">
      <div class="stats-card">
        <div class="stats-icon">
          <i class="bi bi-people"></i>
        </div>
        <div class="stats-info">
          <h3 class="stats-value">${stats.userCount}</h3>
          <p class="stats-label">Total Users</p>
        </div>
      </div>
    </div>

    <div class="col-md-3">
      <div class="stats-card">
        <div class="stats-icon">
          <i class="bi bi-chat-quote"></i>
        </div>
        <div class="stats-info">
          <h3 class="stats-value">${stats.phraseCount}</h3>
          <p class="stats-label">Total Phrases</p>
        </div>
      </div>
    </div>

    <div class="col-md-3">
      <div class="stats-card">
        <div class="stats-icon">
          <i class="bi bi-camera-video"></i>
        </div>
        <div class="stats-info">
          <h3 class="stats-value">${stats.videoCount}</h3>
          <p class="stats-label">Total Videos</p>
        </div>
      </div>
    </div>

    <div class="col-md-3">
      <div class="stats-card">
        <div class="stats-icon">
          <i class="bi bi-tags"></i>
        </div>
        <div class="stats-info">
          <h3 class="stats-value">${stats.categoryCount}</h3>
          <p class="stats-label">Categories</p>
        </div>
      </div>
    </div>

    <!-- Activity Chart -->
    <div class="col-md-8">
      <div class="chart-card">
        <div class="chart-header">
          <h3 class="chart-title">User Activity</h3>
          <div class="chart-actions">
            <button class="btn btn-sm btn-secondary">Daily</button>
            <button class="btn btn-sm btn-outline-secondary">Weekly</button>
            <button class="btn btn-sm btn-outline-secondary">Monthly</button>
          </div>
        </div>
        <div class="chart-body">
          <canvas id="activity-chart"></canvas>
        </div>
      </div>
    </div>

    <!-- Recent Activity -->
    <div class="col-md-4">
      <div class="activity-card">
        <h3 class="activity-title">Recent Activity</h3>
        <ul class="activity-list">
          ${recentActivity.map(activity => `
            <li class="activity-item">
              <div class="activity-icon">
                <i class="bi ${getActivityIcon(activity.type)}"></i>
              </div>
              <div class="activity-content">
                <p class="activity-text">${activity.description}</p>
                <span class="activity-time">${formatTimeAgo(activity.timestamp)}</span>
              </div>
            </li>
          `).join('')}
        </ul>
        <a href="#all-activity" class="activity-link">View All Activity</a>
      </div>
    </div>
  </div>
</div>
```

### 13.2 User Management

```html
<!-- User Management -->
<div class="user-management">
  <div class="section-header">
    <h2 class="section-title">User Management</h2>
    <div class="section-actions">
      <div class="search-container">
        <input type="text" id="user-search" class="search-input neumorphic-input" placeholder="Search users...">
        <i class="bi bi-search search-icon"></i>
      </div>
      <button class="btn btn-primary neumorphic-button" data-bs-toggle="modal" data-bs-target="#add-user-modal">
        <i class="bi bi-person-plus"></i> Add User
      </button>
    </div>
  </div>

  <div class="table-container">
    <table class="table user-table">
      <thead>
        <tr>
          <th>Name</th>
          <th>Email</th>
          <th>Role</th>
          <th>Status</th>
          <th>Joined</th>
          <th>Actions</th>
        </tr>
      </thead>
      <tbody>
        ${users.map(user => `
          <tr>
            <td>
              <div class="user-info">
                <div class="user-avatar">${getInitials(user.name)}</div>
                <span class="user-name">${user.name}</span>
              </div>
            </td>
            <td>${user.email}</td>
            <td>
              <span class="user-role ${user.role === 'admin' ? 'role-admin' : 'role-user'}">
                ${user.role}
              </span>
            </td>
            <td>
              <span class="user-status ${user.active ? 'status-active' : 'status-inactive'}">
                ${user.active ? 'Active' : 'Inactive'}
              </span>
            </td>
            <td>${formatDate(user.createdAt)}</td>
            <td>
              <div class="table-actions">
                <button class="btn-edit" data-id="${user.id}" aria-label="Edit">
                  <i class="bi bi-pencil"></i>
                </button>
                <button class="btn-delete" data-id="${user.id}" aria-label="Delete">
                  <i class="bi bi-trash"></i>
                </button>
              </div>
            </td>
          </tr>
        `).join('')}
      </tbody>
    </table>
  </div>

  <div class="pagination-container">
    <div class="pagination-info">
      Showing <span>${startItem}</span> to <span>${endItem}</span> of <span>${totalItems}</span> users
    </div>
    <div class="pagination">
      <button class="pagination-btn" ${currentPage === 1 ? 'disabled' : ''}>
        <i class="bi bi-chevron-left"></i>
      </button>
      ${generatePaginationButtons(currentPage, totalPages)}
      <button class="pagination-btn" ${currentPage === totalPages ? 'disabled' : ''}>
        <i class="bi bi-chevron-right"></i>
      </button>
    </div>
  </div>
</div>
```

## 14. Offline Capabilities Implementation

### 14.1 Service Worker Configuration

```javascript
// Register service worker
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('./sw.js')
      .then(registration => {
        console.log('Service Worker registered with scope:', registration.scope);
      })
      .catch(error => {
        console.error('Service Worker registration failed:', error);
      });
  });
}

// Check for updates
const checkForUpdates = async () => {
  if ('serviceWorker' in navigator) {
    const registration = await navigator.serviceWorker.ready;

    // Check for updates
    registration.update();

    // Listen for new service worker
    registration.addEventListener('updatefound', () => {
      const newWorker = registration.installing;

      newWorker.addEventListener('statechange', () => {
        if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
          // New service worker available
          showUpdateNotification();
        }
      });
    });
  }
};

// Show update notification
const showUpdateNotification = () => {
  const notification = document.createElement('div');
  notification.className = 'update-notification';
  notification.innerHTML = `
    <div class="update-notification-content">
      <i class="bi bi-arrow-clockwise"></i>
      <span>New version available!</span>
      <button id="update-button" class="btn btn-sm btn-primary">Update Now</button>
    </div>
  `;

  document.body.appendChild(notification);

  // Add event listener to update button
  document.getElementById('update-button').addEventListener('click', () => {
    window.location.reload();
  });
};

// Initialize offline sync
const initOfflineSync = async () => {
  if ('serviceWorker' in navigator && 'SyncManager' in window) {
    try {
      // Register sync event
      const registration = await navigator.serviceWorker.ready;

      // Register periodic sync if available
      if ('periodicSync' in registration) {
        const status = await navigator.permissions.query({
          name: 'periodic-background-sync',
        });

        if (status.state === 'granted') {
          await registration.periodicSync.register('sync-data', {
            minInterval: 24 * 60 * 60 * 1000, // 24 hours
          });
        }
      }

      // Register one-time sync
      await registration.sync.register('sync-data');

      return true;
    } catch (error) {
      console.error('Failed to register sync:', error);
      return false;
    }
  }

  return false;
};

// Manifest.json
const manifest = {
  "name": "INGLESyLATINOS.com",
  "short_name": "INGLESyLATINOS",
  "description": "Learn English with perfect pronunciation",
  "start_url": "/index.html",
  "display": "standalone",
  "background_color": "#d9e3f1",
  "theme_color": "#378dfc",
  "icons": [
    {
      "src": "img/logo/logo-192.png",
      "sizes": "192x192",
      "type": "image/png",
      "purpose": "any maskable"
    },
    {
      "src": "img/logo/logo-512.png",
      "sizes": "512x512",
      "type": "image/png",
      "purpose": "any maskable"
    }
  ]
};
```

### 14.2 Offline UI Components

```html
<!-- Connection Status Indicator -->
<div class="connection-status">
  <div class="connection-indicator ${navigator.onLine ? 'online' : 'offline'}">
    <i class="bi ${navigator.onLine ? 'bi-wifi' : 'bi-wifi-off'}"></i>
  </div>
  <div class="connection-tooltip">
    ${navigator.onLine ? 'Online' : 'Offline Mode'}
  </div>
</div>

<!-- Sync Status Indicator -->
<div class="sync-status">
  <div class="sync-indicator ${syncStatus}">
    <i class="bi ${getSyncIcon(syncStatus)}"></i>
    ${pendingOperations > 0 ? `<span class="sync-badge">${pendingOperations}</span>` : ''}
  </div>
  <div class="sync-tooltip">
    ${getSyncTooltip(syncStatus, pendingOperations)}
  </div>
</div>

<!-- Offline Banner -->
<div class="offline-banner ${navigator.onLine ? 'd-none' : ''}">
  <i class="bi bi-wifi-off"></i>
  <span>You are currently offline. Some features may be limited.</span>
  <button class="btn btn-sm btn-light retry-connection-btn">
    <i class="bi bi-arrow-repeat"></i> Retry
  </button>
</div>

<!-- Offline Page -->
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Offline - INGLESyLATINOS.com</title>
  <link rel="stylesheet" href="css/bootstrap-morph.css">
  <link rel="stylesheet" href="css/custom-components.css">
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css">
</head>
<body>
  <div class="offline-container">
    <div class="offline-content">
      <div class="offline-icon">
        <i class="bi bi-wifi-off"></i>
      </div>
      <h1 class="offline-title">You're Offline</h1>
      <p class="offline-message">
        Please check your internet connection and try again.
      </p>
      <div class="offline-actions">
        <button class="btn btn-primary neumorphic-button retry-btn">
          <i class="bi bi-arrow-repeat"></i> Retry Connection
        </button>
        <a href="index.html" class="btn btn-secondary neumorphic-button">
          <i class="bi bi-house"></i> Go to Homepage
        </a>
      </div>
    </div>
  </div>

  <script>
    // Check connection status
    function checkConnection() {
      if (navigator.onLine) {
        window.location.reload();
      }
    }

    // Add event listeners
    window.addEventListener('online', checkConnection);

    document.querySelector('.retry-btn').addEventListener('click', checkConnection);
  </script>
</body>
</html>
```

## 15. Testing and Quality Assurance

### 15.1 Testing Strategy

1. **Unit Testing**
   - Test individual components and services
   - Use Jest for JavaScript testing
   - Test core functionality like authentication, data management, and speech services

2. **Integration Testing**
   - Test interactions between components
   - Ensure data flows correctly between services
   - Verify API integrations work as expected

3. **End-to-End Testing**
   - Use Cypress for E2E testing
   - Test user flows from start to finish
   - Verify critical paths like authentication, phrase management, and video playback

4. **Offline Testing**
   - Test application behavior when offline
   - Verify data synchronization when connection is restored
   - Ensure offline indicators work correctly

5. **Cross-Browser Testing**
   - Test on Chrome, Firefox, Safari, and Edge
   - Ensure consistent behavior across browsers
   - Verify PWA features work on supported browsers

6. **Mobile Testing**
   - Test on various mobile devices and screen sizes
   - Verify touch interactions work correctly
   - Test PWA installation on mobile devices

7. **Accessibility Testing**
   - Use automated tools like Lighthouse and axe
   - Perform manual testing with screen readers
   - Ensure WCAG 2.1 AA compliance

8. **Performance Testing**
   - Measure load times and rendering performance
   - Test with different network conditions
   - Optimize for low-end devices

### 15.2 Performance Optimization

1. **Asset Optimization**
   - Compress images using WebP format
   - Minify CSS and JavaScript
   - Use tree-shaking to eliminate unused code
   - Implement code splitting for faster initial load

2. **Caching Strategy**
   - Implement effective cache policies
   - Use service worker for asset caching
   - Implement IndexedDB for data caching
   - Use memory caching for frequently accessed data

3. **Lazy Loading**
   - Lazy load images and videos
   - Implement code splitting for routes
   - Defer non-critical JavaScript
   - Use intersection observer for content loading

4. **Network Optimization**
   - Reduce API calls with data batching
   - Implement request caching
   - Use compression for API responses
   - Optimize for low bandwidth conditions

5. **Rendering Optimization**
   - Minimize DOM manipulations
   - Use efficient CSS selectors
   - Implement virtual scrolling for long lists
   - Optimize animations for performance

6. **Monitoring and Analytics**
   - Implement real user monitoring (RUM)
   - Track core web vitals
   - Set up performance budgets
   - Use analytics to identify bottlenecks

## 16. Deployment and Maintenance

### 16.1 Deployment Process

1. **Build Process**
   - Set up automated build pipeline
   - Configure environment-specific variables
   - Minify and optimize assets
   - Generate service worker

2. **Hosting Setup**
   - Configure web server for static hosting
   - Set up SSL certificate
   - Configure proper MIME types
   - Implement HTTP/2 for improved performance

3. **Deployment Automation**
   - Implement continuous integration/continuous deployment (CI/CD)
   - Set up staging environment for testing
   - Implement blue-green deployment for zero downtime
   - Configure automated rollback for failed deployments

4. **Post-Deployment Verification**
   - Run automated tests after deployment
   - Verify critical functionality
   - Check performance metrics
   - Monitor error rates

### 16.2 Maintenance Plan

1. **Regular Updates**
   - Schedule monthly dependency updates
   - Implement security patches promptly
   - Plan quarterly feature releases
   - Document all changes in changelog

2. **Monitoring and Alerting**
   - Set up uptime monitoring
   - Implement error tracking
   - Configure performance alerts
   - Monitor API usage and limits

3. **Backup Strategy**
   - Implement daily database backups
   - Store backups in multiple locations
   - Test backup restoration regularly
   - Document backup and recovery procedures

4. **User Feedback Loop**
   - Collect user feedback through in-app surveys
   - Analyze usage patterns
   - Prioritize improvements based on feedback
   - Communicate changes to users

5. **Documentation**
   - Maintain up-to-date technical documentation
   - Create user guides and tutorials
   - Document API endpoints
   - Keep codebase well-commented

## 17. Conclusion

This comprehensive development plan provides a structured approach to rebuilding the INGLESyLATINOS.com website from scratch. By establishing the Bootswatch Morph theme as the foundation from the beginning, we ensure consistent neumorphic styling throughout the application.

The plan addresses potential theme conflicts upfront, provides a clear directory structure, and details all core functionalities. The step-by-step implementation roadmap allows for sequential development without the need for backtracking or refactoring.

By following this plan, the development team can create a modern, responsive, and feature-rich application that provides an excellent user experience for learning English pronunciation. The focus on offline capabilities ensures that users can continue learning even without an internet connection, while the admin panel provides powerful tools for content management.

The emphasis on performance optimization and accessibility ensures that the application is fast, efficient, and usable by all users, regardless of their device or abilities. The comprehensive testing strategy and maintenance plan ensure the long-term success and sustainability of the application.

With this plan as a guide, the INGLESyLATINOS.com rebuild will result in a high-quality, professional application that meets all the requirements and provides a solid foundation for future growth and expansion.