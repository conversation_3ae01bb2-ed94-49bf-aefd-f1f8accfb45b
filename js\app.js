/* Main Application Controller for INGLESyLATINOS.com */

const App = (function() {
  // Application state
  let currentUser = null;
  let isOnline = navigator.onLine;
  let currentPage = 'landing';
  
  // DOM elements
  let elements = {};
  
  // Initialize application
  const initialize = async () => {
    console.log('Initializing INGLESyLATINOS.com...');
    
    try {
      // Cache DOM elements
      cacheElements();
      
      // Set up event listeners
      setupEventListeners();
      
      // Initialize services
      await initializeServices();
      
      // Check authentication status
      await checkAuthStatus();
      
      // Set up router
      Router.initialize();
      
      // Initialize offline manager
      OfflineManager.initialize();
      
      // Set up scroll animations
      setupScrollAnimations();
      
      // Show connection status
      updateConnectionStatus();
      
      console.log('Application initialized successfully');
      
    } catch (error) {
      console.error('Error initializing application:', error);
      showError('Error al inicializar la aplicación. Por favor, recarga la página.');
    }
  };
  
  // Cache frequently used DOM elements
  const cacheElements = () => {
    elements = {
      // Navigation
      loginBtn: document.getElementById('loginBtn'),
      startBtn: document.getElementById('startBtn'),
      registerBtn: document.getElementById('registerBtn'),
      
      // Pages
      landingPage: document.getElementById('landing-page'),
      authPage: document.getElementById('auth-page'),
      phrasesPage: document.getElementById('phrases-page'),
      dashboardPage: document.getElementById('dashboard-page'),
      
      // UI components
      connectionStatus: document.getElementById('connection-status'),
      loadingSpinner: document.getElementById('loading-spinner'),
      
      // App container
      appContainer: document.getElementById('app-container')
    };
  };
  
  // Set up global event listeners
  const setupEventListeners = () => {
    // Navigation buttons
    if (elements.loginBtn) {
      elements.loginBtn.addEventListener('click', (e) => {
        e.preventDefault();
        Router.navigate('auth', { mode: 'login' });
      });
    }
    
    if (elements.startBtn) {
      elements.startBtn.addEventListener('click', (e) => {
        e.preventDefault();
        if (currentUser) {
          Router.navigate('phrases');
        } else {
          Router.navigate('auth', { mode: 'register' });
        }
      });
    }
    
    if (elements.registerBtn) {
      elements.registerBtn.addEventListener('click', (e) => {
        e.preventDefault();
        Router.navigate('auth', { mode: 'register' });
      });
    }
    
    // Connection status
    window.addEventListener('online', () => {
      isOnline = true;
      updateConnectionStatus();
      OfflineManager.syncPendingData();
    });
    
    window.addEventListener('offline', () => {
      isOnline = false;
      updateConnectionStatus();
    });
    
    // Keyboard shortcuts
    document.addEventListener('keydown', handleKeyboardShortcuts);
    
    // Prevent zoom on double tap (mobile)
    let lastTouchEnd = 0;
    document.addEventListener('touchend', (event) => {
      const now = (new Date()).getTime();
      if (now - lastTouchEnd <= 300) {
        event.preventDefault();
      }
      lastTouchEnd = now;
    }, false);
  };
  
  // Initialize all services
  const initializeServices = async () => {
    try {
      // Initialize data manager
      await DataManager.initialize();
      
      // Initialize authentication service
      AuthService.initialize();
      
      // Initialize web speech service
      WebSpeechService.initialize();
      
      console.log('All services initialized');
    } catch (error) {
      console.error('Error initializing services:', error);
      throw error;
    }
  };
  
  // Check if user is authenticated
  const checkAuthStatus = async () => {
    try {
      const token = AuthService.getToken();
      if (token && AuthService.isTokenValid(token)) {
        currentUser = await AuthService.getCurrentUser();
        updateUIForAuthenticatedUser();
      } else {
        updateUIForGuestUser();
      }
    } catch (error) {
      console.error('Error checking auth status:', error);
      updateUIForGuestUser();
    }
  };
  
  // Update UI for authenticated user
  const updateUIForAuthenticatedUser = () => {
    if (elements.loginBtn) {
      elements.loginBtn.textContent = 'Mi Cuenta';
      elements.loginBtn.href = '#dashboard';
    }
  };
  
  // Update UI for guest user
  const updateUIForGuestUser = () => {
    if (elements.loginBtn) {
      elements.loginBtn.textContent = 'Iniciar Sesión';
      elements.loginBtn.href = '#login';
    }
  };
  
  // Handle keyboard shortcuts
  const handleKeyboardShortcuts = (event) => {
    // Ctrl/Cmd + K for search
    if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
      event.preventDefault();
      // TODO: Open search modal
    }
    
    // Escape key to close modals
    if (event.key === 'Escape') {
      closeAllModals();
    }
  };
  
  // Close all open modals
  const closeAllModals = () => {
    const modals = document.querySelectorAll('.modal.show');
    modals.forEach(modal => {
      const bsModal = bootstrap.Modal.getInstance(modal);
      if (bsModal) {
        bsModal.hide();
      }
    });
  };
  
  // Set up scroll animations
  const setupScrollAnimations = () => {
    const observerOptions = {
      threshold: 0.1,
      rootMargin: '0px 0px -50px 0px'
    };
    
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          entry.target.classList.add('in-view');
        }
      });
    }, observerOptions);
    
    // Observe elements with scroll-animate class
    document.querySelectorAll('.scroll-animate').forEach(el => {
      observer.observe(el);
    });
  };
  
  // Update connection status indicator
  const updateConnectionStatus = () => {
    if (elements.connectionStatus) {
      if (isOnline) {
        elements.connectionStatus.classList.add('d-none');
      } else {
        elements.connectionStatus.classList.remove('d-none');
      }
    }
  };
  
  // Show loading spinner
  const showLoading = () => {
    if (elements.loadingSpinner) {
      elements.loadingSpinner.classList.remove('d-none');
    }
  };
  
  // Hide loading spinner
  const hideLoading = () => {
    if (elements.loadingSpinner) {
      elements.loadingSpinner.classList.add('d-none');
    }
  };
  
  // Show error message
  const showError = (message) => {
    // Create toast notification
    const toast = createToast('error', message);
    document.body.appendChild(toast);
    
    // Auto remove after 5 seconds
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, 5000);
  };
  
  // Show success message
  const showSuccess = (message) => {
    const toast = createToast('success', message);
    document.body.appendChild(toast);
    
    setTimeout(() => {
      if (toast.parentNode) {
        toast.parentNode.removeChild(toast);
      }
    }, 3000);
  };
  
  // Create toast notification
  const createToast = (type, message) => {
    const toast = document.createElement('div');
    toast.className = `toast-notification toast-${type}`;
    toast.innerHTML = `
      <div class="toast-content">
        <i class="bi bi-${type === 'error' ? 'exclamation-triangle' : 'check-circle'}"></i>
        <span>${message}</span>
        <button class="toast-close" onclick="this.parentElement.parentElement.remove()">
          <i class="bi bi-x"></i>
        </button>
      </div>
    `;
    
    // Add styles
    toast.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      background: ${type === 'error' ? 'var(--app-error)' : 'var(--app-success)'};
      color: white;
      padding: 1rem;
      border-radius: var(--bs-border-radius);
      box-shadow: var(--bs-box-shadow-lg);
      z-index: var(--z-toast);
      max-width: 300px;
      animation: slideInRight 0.3s ease;
    `;
    
    return toast;
  };
  
  // Public API
  return {
    initialize,
    showLoading,
    hideLoading,
    showError,
    showSuccess,
    getCurrentUser: () => currentUser,
    setCurrentUser: (user) => { currentUser = user; },
    isOnline: () => isOnline,
    getCurrentPage: () => currentPage,
    setCurrentPage: (page) => { currentPage = page; }
  };
})();

// Add CSS for toast notifications
const toastStyles = document.createElement('style');
toastStyles.textContent = `
  @keyframes slideInRight {
    from {
      transform: translateX(100%);
      opacity: 0;
    }
    to {
      transform: translateX(0);
      opacity: 1;
    }
  }
  
  .toast-notification {
    animation: slideInRight 0.3s ease;
  }
  
  .toast-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
  
  .toast-close {
    background: none;
    border: none;
    color: inherit;
    cursor: pointer;
    margin-left: auto;
    padding: 0;
    font-size: 1.2rem;
  }
`;
document.head.appendChild(toastStyles);
