/* Custom Neumorphic Components for INGLESyLATINOS.com */

/* Base neumorphic card styling */
.neumorphic-card {
  background-color: var(--bs-tertiary-bg);
  border-radius: var(--bs-border-radius);
  border: none;
  box-shadow: var(--bs-box-shadow);
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);
  padding: 1.5rem;
}

.neumorphic-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--bs-box-shadow-lg);
}

/* Neumorphic buttons */
.btn-neumorphic {
  background-color: var(--bs-tertiary-bg);
  color: var(--bs-body-color);
  border: none;
  border-radius: var(--bs-border-radius-pill);
  padding: 0.75rem 1.5rem;
  font-weight: var(--bs-font-weight-semibold);
  box-shadow: var(--bs-box-shadow);
  transition: all var(--transition-normal) ease;
  text-decoration: none;
  display: inline-block;
}

.btn-neumorphic:hover {
  transform: translateY(-2px);
  color: var(--bs-body-color);
  text-decoration: none;
}

.btn-neumorphic:active {
  transform: translateY(0);
  box-shadow: var(--bs-box-shadow-inset);
}

/* Neumorphic form inputs */
.form-control-neumorphic {
  background-color: var(--bs-tertiary-bg);
  border: none;
  border-radius: var(--bs-border-radius);
  padding: 0.75rem 1rem;
  box-shadow: var(--bs-box-shadow-inset);
  transition: all var(--transition-normal) ease;
  color: var(--bs-body-color);
}

.form-control-neumorphic:focus {
  outline: none;
  box-shadow: var(--bs-box-shadow-inset), 0 0 0 3px rgba(55, 141, 252, 0.25);
  background-color: var(--bs-tertiary-bg);
  color: var(--bs-body-color);
}

.form-control-neumorphic::placeholder {
  color: rgba(123, 138, 184, 0.6);
}

/* Phrase cards - mobile optimized */
.phrase-card {
  background-color: var(--bs-tertiary-bg);
  border-radius: var(--bs-border-radius-lg);
  box-shadow: var(--bs-box-shadow);
  padding: 2rem;
  margin-bottom: 1.5rem;
  transition: all var(--transition-normal) ease;
  cursor: pointer;
  min-height: 200px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
}

.phrase-card:hover {
  transform: translateY(-3px);
  box-shadow: var(--bs-box-shadow-lg);
}

.phrase-english {
  font-size: 1.5rem;
  font-weight: var(--bs-font-weight-bold);
  color: var(--bs-dark);
  margin-bottom: 1rem;
  line-height: 1.3;
}

.phrase-phonetic {
  font-size: 1.25rem;
  font-weight: var(--bs-font-weight-semibold);
  color: var(--phonetic-color);
  background-color: var(--phonetic-bg);
  border: 2px solid var(--phonetic-border);
  border-radius: var(--bs-border-radius);
  padding: 0.75rem;
  margin-bottom: 1rem;
  cursor: pointer;
  transition: all var(--transition-normal) ease;
  font-family: 'Courier New', monospace;
}

.phrase-phonetic:hover {
  background-color: var(--bs-primary);
  color: white;
  transform: scale(1.02);
}

.phrase-spanish {
  font-size: 1.25rem;
  font-weight: var(--bs-font-weight-normal);
  color: var(--bs-body-color);
  font-style: italic;
  margin-bottom: 1rem;
}

.phrase-actions {
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: auto;
}

.phrase-actions .btn {
  border-radius: 50%;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--bs-box-shadow-sm);
}

/* Feature cards */
.feature-card {
  background-color: var(--bs-tertiary-bg);
  border-radius: var(--bs-border-radius-lg);
  box-shadow: var(--bs-box-shadow);
  padding: 2rem;
  text-align: center;
  transition: all var(--transition-normal) ease;
  height: 100%;
}

.feature-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--bs-box-shadow-lg);
}

.feature-icon {
  width: 80px;
  height: 80px;
  background-color: var(--bs-primary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  box-shadow: var(--bs-box-shadow);
}

.feature-icon i {
  font-size: 2rem;
  color: white;
}

.feature-card h3 {
  font-size: 1.25rem;
  font-weight: var(--bs-font-weight-bold);
  margin-bottom: 1rem;
  color: var(--bs-dark);
}

.feature-card p {
  color: var(--bs-body-color);
  margin-bottom: 0;
}

/* Navigation enhancements */
.navbar {
  background-color: rgba(240, 245, 250, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: var(--bs-box-shadow-sm);
}

.navbar-brand {
  font-weight: var(--bs-font-weight-bold);
  color: var(--bs-primary) !important;
}

/* Connection status indicator */
.connection-status {
  position: fixed;
  top: 70px;
  right: 1rem;
  background-color: var(--app-warning);
  color: var(--bs-dark);
  padding: 0.5rem 1rem;
  border-radius: var(--bs-border-radius-pill);
  box-shadow: var(--bs-box-shadow);
  z-index: var(--z-toast);
  font-size: 0.875rem;
  font-weight: var(--bs-font-weight-semibold);
}

.connection-status i {
  margin-right: 0.5rem;
}

/* Loading spinner */
.loading-spinner {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: var(--z-modal);
  background-color: rgba(240, 245, 250, 0.9);
  padding: 2rem;
  border-radius: var(--bs-border-radius-lg);
  box-shadow: var(--bs-box-shadow-lg);
}

/* Progress indicators */
.progress-neumorphic {
  background-color: var(--bs-tertiary-bg);
  border-radius: var(--bs-border-radius-pill);
  box-shadow: var(--bs-box-shadow-inset);
  overflow: hidden;
}

.progress-neumorphic .progress-bar {
  background: linear-gradient(90deg, var(--bs-primary), var(--app-success));
  border-radius: var(--bs-border-radius-pill);
  box-shadow: var(--bs-box-shadow-sm);
}

/* Achievement badges */
.achievement-badge {
  background-color: var(--bs-tertiary-bg);
  border-radius: 50%;
  width: 60px;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: var(--bs-box-shadow);
  margin: 0 auto;
  transition: all var(--transition-normal) ease;
}

.achievement-badge:hover {
  transform: scale(1.1);
  box-shadow: var(--bs-box-shadow-lg);
}

.achievement-badge.earned {
  background: linear-gradient(135deg, var(--app-success), var(--bs-primary));
  color: white;
}

.achievement-badge i {
  font-size: 1.5rem;
}

/* Responsive utilities */
@media (max-width: 768px) {
  .phrase-card {
    padding: 1.5rem;
    min-height: 180px;
  }
  
  .phrase-english {
    font-size: 1.25rem;
  }
  
  .phrase-phonetic {
    font-size: 1.1rem;
  }
  
  .phrase-spanish {
    font-size: 1.1rem;
  }
  
  .feature-card {
    padding: 1.5rem;
  }
  
  .feature-icon {
    width: 60px;
    height: 60px;
  }
  
  .feature-icon i {
    font-size: 1.5rem;
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .phrase-card,
  .feature-card,
  .btn-neumorphic,
  .achievement-badge {
    transition: none;
  }
  
  .phrase-card:hover,
  .feature-card:hover,
  .btn-neumorphic:hover,
  .achievement-badge:hover {
    transform: none;
  }
}

/* Focus indicators for keyboard navigation */
.phrase-card:focus,
.btn-neumorphic:focus,
.form-control-neumorphic:focus {
  outline: 2px solid var(--bs-primary);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .phrase-phonetic {
    border-width: 3px;
  }
  
  .feature-icon {
    border: 2px solid var(--bs-primary);
  }
}
