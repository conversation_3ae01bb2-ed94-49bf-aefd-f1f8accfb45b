/* Web Speech API Service for INGLESyLATINOS.com */

const WebSpeechService = (function() {
  // Speech synthesis
  let synth = null;
  let voices = [];
  let currentVoice = null;
  
  // Speech recognition
  let recognition = null;
  let isListening = false;
  
  // Settings
  let settings = {
    rate: 0.8,
    pitch: 1,
    volume: 1,
    lang: 'en-US',
    voiceType: 'female' // 'male' or 'female'
  };
  
  // Initialize service
  const initialize = () => {
    console.log('Initializing WebSpeechService...');
    
    try {
      // Initialize speech synthesis
      initializeSynthesis();
      
      // Initialize speech recognition
      initializeRecognition();
      
      // Load user settings
      loadSettings();
      
      console.log('WebSpeechService initialized successfully');
      
    } catch (error) {
      console.error('Error initializing WebSpeechService:', error);
    }
  };
  
  // Initialize speech synthesis
  const initializeSynthesis = () => {
    if ('speechSynthesis' in window) {
      synth = window.speechSynthesis;
      
      // Load voices when they become available
      loadVoices();
      
      // Some browsers load voices asynchronously
      if (synth.onvoiceschanged !== undefined) {
        synth.onvoiceschanged = loadVoices;
      }
      
      console.log('Speech synthesis initialized');
    } else {
      console.warn('Speech synthesis not supported');
    }
  };
  
  // Load available voices
  const loadVoices = () => {
    voices = synth.getVoices();
    
    // Filter English voices
    const englishVoices = voices.filter(voice => 
      voice.lang.startsWith('en-') && voice.localService
    );
    
    if (englishVoices.length > 0) {
      // Try to find a preferred voice
      currentVoice = findPreferredVoice(englishVoices);
      console.log('Selected voice:', currentVoice?.name);
    }
    
    console.log(`Loaded ${voices.length} voices, ${englishVoices.length} English voices`);
  };
  
  // Find preferred voice based on settings
  const findPreferredVoice = (englishVoices) => {
    // Try to find voice matching user preference
    const preferredVoices = englishVoices.filter(voice => {
      const name = voice.name.toLowerCase();
      if (settings.voiceType === 'female') {
        return name.includes('female') || name.includes('woman') || 
               name.includes('samantha') || name.includes('karen') ||
               name.includes('susan') || name.includes('victoria');
      } else {
        return name.includes('male') || name.includes('man') ||
               name.includes('alex') || name.includes('daniel') ||
               name.includes('tom') || name.includes('david');
      }
    });
    
    if (preferredVoices.length > 0) {
      return preferredVoices[0];
    }
    
    // Fallback to first available English voice
    return englishVoices[0];
  };
  
  // Initialize speech recognition
  const initializeRecognition = () => {
    if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition;
      recognition = new SpeechRecognition();
      
      recognition.continuous = false;
      recognition.interimResults = false;
      recognition.lang = 'en-US';
      
      recognition.onstart = () => {
        isListening = true;
        console.log('Speech recognition started');
      };
      
      recognition.onend = () => {
        isListening = false;
        console.log('Speech recognition ended');
      };
      
      recognition.onerror = (event) => {
        console.error('Speech recognition error:', event.error);
        isListening = false;
      };
      
      console.log('Speech recognition initialized');
    } else {
      console.warn('Speech recognition not supported');
    }
  };
  
  // Load user settings
  const loadSettings = async () => {
    try {
      const savedSettings = await DataManager.getUserData('speechSettings');
      if (savedSettings) {
        settings = { ...settings, ...savedSettings };
      }
    } catch (error) {
      console.error('Error loading speech settings:', error);
    }
  };
  
  // Save user settings
  const saveSettings = async () => {
    try {
      await DataManager.setUserData('speechSettings', settings);
    } catch (error) {
      console.error('Error saving speech settings:', error);
    }
  };
  
  // Speak text
  const speak = (text, options = {}) => {
    if (!synth || !text) {
      console.warn('Speech synthesis not available or no text provided');
      return Promise.reject(new Error('Speech synthesis not available'));
    }
    
    return new Promise((resolve, reject) => {
      // Cancel any ongoing speech
      synth.cancel();
      
      // Create utterance
      const utterance = new SpeechSynthesisUtterance(text);
      
      // Set voice
      if (currentVoice) {
        utterance.voice = currentVoice;
      }
      
      // Set properties
      utterance.rate = options.rate || settings.rate;
      utterance.pitch = options.pitch || settings.pitch;
      utterance.volume = options.volume || settings.volume;
      utterance.lang = options.lang || settings.lang;
      
      // Event handlers
      utterance.onend = () => {
        console.log('Speech finished');
        resolve();
      };
      
      utterance.onerror = (event) => {
        console.error('Speech error:', event.error);
        reject(new Error(event.error));
      };
      
      // Speak
      synth.speak(utterance);
      
      console.log('Speaking:', text);
    });
  };
  
  // Stop speaking
  const stopSpeaking = () => {
    if (synth) {
      synth.cancel();
    }
  };
  
  // Start listening for speech
  const startListening = (callback) => {
    if (!recognition) {
      console.warn('Speech recognition not available');
      return Promise.reject(new Error('Speech recognition not available'));
    }
    
    return new Promise((resolve, reject) => {
      if (isListening) {
        reject(new Error('Already listening'));
        return;
      }
      
      recognition.onresult = (event) => {
        const result = event.results[0][0];
        const transcript = result.transcript;
        const confidence = result.confidence;
        
        console.log('Speech recognized:', transcript, 'Confidence:', confidence);
        
        if (callback) {
          callback(transcript, confidence);
        }
        
        resolve({ transcript, confidence });
      };
      
      recognition.onerror = (event) => {
        reject(new Error(event.error));
      };
      
      recognition.start();
    });
  };
  
  // Stop listening
  const stopListening = () => {
    if (recognition && isListening) {
      recognition.stop();
    }
  };
  
  // Compare pronunciation
  const comparePronunciation = async (targetText, spokenText) => {
    // Simple comparison algorithm
    // In production, you might want to use more sophisticated phonetic comparison
    
    const normalize = (text) => {
      return text.toLowerCase()
        .replace(/[^\w\s]/g, '')
        .replace(/\s+/g, ' ')
        .trim();
    };
    
    const target = normalize(targetText);
    const spoken = normalize(spokenText);
    
    // Calculate similarity using Levenshtein distance
    const similarity = calculateSimilarity(target, spoken);
    
    let score = 0;
    let feedback = '';
    
    if (similarity >= 0.9) {
      score = 100;
      feedback = '¡Excelente pronunciación!';
    } else if (similarity >= 0.8) {
      score = 85;
      feedback = '¡Muy bien! Casi perfecto.';
    } else if (similarity >= 0.7) {
      score = 70;
      feedback = 'Bien. Sigue practicando.';
    } else if (similarity >= 0.5) {
      score = 50;
      feedback = 'Necesitas más práctica.';
    } else {
      score = 25;
      feedback = 'Intenta de nuevo.';
    }
    
    return {
      score,
      feedback,
      similarity,
      target: targetText,
      spoken: spokenText
    };
  };
  
  // Calculate text similarity
  const calculateSimilarity = (str1, str2) => {
    const longer = str1.length > str2.length ? str1 : str2;
    const shorter = str1.length > str2.length ? str2 : str1;
    
    if (longer.length === 0) {
      return 1.0;
    }
    
    const distance = levenshteinDistance(longer, shorter);
    return (longer.length - distance) / longer.length;
  };
  
  // Levenshtein distance algorithm
  const levenshteinDistance = (str1, str2) => {
    const matrix = [];
    
    for (let i = 0; i <= str2.length; i++) {
      matrix[i] = [i];
    }
    
    for (let j = 0; j <= str1.length; j++) {
      matrix[0][j] = j;
    }
    
    for (let i = 1; i <= str2.length; i++) {
      for (let j = 1; j <= str1.length; j++) {
        if (str2.charAt(i - 1) === str1.charAt(j - 1)) {
          matrix[i][j] = matrix[i - 1][j - 1];
        } else {
          matrix[i][j] = Math.min(
            matrix[i - 1][j - 1] + 1,
            matrix[i][j - 1] + 1,
            matrix[i - 1][j] + 1
          );
        }
      }
    }
    
    return matrix[str2.length][str1.length];
  };
  
  // Update settings
  const updateSettings = async (newSettings) => {
    settings = { ...settings, ...newSettings };
    
    // Update voice if voice type changed
    if (newSettings.voiceType && voices.length > 0) {
      const englishVoices = voices.filter(voice => voice.lang.startsWith('en-'));
      currentVoice = findPreferredVoice(englishVoices);
    }
    
    // Save settings
    await saveSettings();
  };
  
  // Get current settings
  const getSettings = () => {
    return { ...settings };
  };
  
  // Get available voices
  const getVoices = () => {
    return voices.filter(voice => voice.lang.startsWith('en-'));
  };
  
  // Check if speech synthesis is supported
  const isSynthesisSupported = () => {
    return 'speechSynthesis' in window;
  };
  
  // Check if speech recognition is supported
  const isRecognitionSupported = () => {
    return 'webkitSpeechRecognition' in window || 'SpeechRecognition' in window;
  };
  
  // Public API
  return {
    initialize,
    speak,
    stopSpeaking,
    startListening,
    stopListening,
    comparePronunciation,
    updateSettings,
    getSettings,
    getVoices,
    isSynthesisSupported,
    isRecognitionSupported,
    isListening: () => isListening
  };
})();
