/* Theme Variables for INGLESyLATINOS.com */
/* Based on Bootswatch Morph theme with custom enhancements */

:root {
  /* Primary Morph theme variables */
  --bs-body-bg: #d9e3f1;
  --bs-body-color: #7b8ab8;
  --bs-tertiary-bg: #f0f5fa;
  --bs-primary: #378dfc;
  --bs-secondary: #d9e3f1;
  --bs-success: #28a745;
  --bs-info: #17a2b8;
  --bs-warning: #ffc107;
  --bs-danger: #dc3545;
  --bs-light: #f8f9fa;
  --bs-dark: #343a40;
  
  /* Border radius */
  --bs-border-radius: 0.375rem;
  --bs-border-radius-sm: 0.25rem;
  --bs-border-radius-lg: 0.5rem;
  --bs-border-radius-xl: 1rem;
  --bs-border-radius-pill: 50rem;
  
  /* Neumorphic shadows */
  --bs-box-shadow: 5px 5px 10px rgba(55, 94, 148, 0.2), -5px -5px 10px rgba(255, 255, 255, 0.4);
  --bs-box-shadow-sm: 2px 2px 5px rgba(55, 94, 148, 0.15), -2px -2px 5px rgba(255, 255, 255, 0.3);
  --bs-box-shadow-lg: 8px 8px 40px rgba(0, 0, 0, 0.15), -5px -5px 10px rgba(255, 255, 255, 0.4);
  --bs-box-shadow-inset: inset 2px 2px 8px rgba(55, 94, 148, 0.3), inset -3px -2px 5px rgba(255, 255, 255, 0.8);
  
  /* Typography */
  --bs-font-sans-serif: Nunito, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
  --bs-font-weight-light: 300;
  --bs-font-weight-normal: 400;
  --bs-font-weight-semibold: 600;
  --bs-font-weight-bold: 700;
  
  /* Custom app colors */
  --app-accent: #ff6b6b;
  --app-success: #51cf66;
  --app-warning: #ffd43b;
  --app-error: #ff6b6b;
  --app-info: #74c0fc;
  
  /* Phonetic text colors */
  --phonetic-bg: #e3f2fd;
  --phonetic-color: #1565c0;
  --phonetic-border: #90caf9;
  
  /* Progress colors */
  --progress-beginner: #ff9800;
  --progress-intermediate: #2196f3;
  --progress-advanced: #4caf50;
  --progress-expert: #9c27b0;
  
  /* Animation durations */
  --transition-fast: 0.15s;
  --transition-normal: 0.3s;
  --transition-slow: 0.5s;
  
  /* Z-index layers */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  
  /* Spacing scale */
  --space-xs: 0.25rem;
  --space-sm: 0.5rem;
  --space-md: 1rem;
  --space-lg: 1.5rem;
  --space-xl: 3rem;
  --space-xxl: 4.5rem;
  
  /* Container max widths */
  --container-sm: 540px;
  --container-md: 720px;
  --container-lg: 960px;
  --container-xl: 1140px;
  --container-xxl: 1320px;
}

/* Dark mode variables (for future implementation) */
@media (prefers-color-scheme: dark) {
  :root {
    --bs-body-bg: #2c3e50;
    --bs-body-color: #ecf0f1;
    --bs-tertiary-bg: #34495e;
    --bs-primary: #3498db;
    --bs-secondary: #95a5a6;
    
    --bs-box-shadow: 5px 5px 10px rgba(0, 0, 0, 0.3), -5px -5px 10px rgba(255, 255, 255, 0.1);
    --bs-box-shadow-inset: inset 2px 2px 8px rgba(0, 0, 0, 0.4), inset -3px -2px 5px rgba(255, 255, 255, 0.1);
    
    --phonetic-bg: #1a252f;
    --phonetic-color: #74c0fc;
    --phonetic-border: #495057;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  :root {
    --bs-box-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
    --bs-box-shadow-inset: inset 1px 1px 2px rgba(0, 0, 0, 0.8);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  :root {
    --transition-fast: 0s;
    --transition-normal: 0s;
    --transition-slow: 0s;
  }
}

/* Font size scale */
.text-xs { font-size: 0.75rem; }
.text-sm { font-size: 0.875rem; }
.text-base { font-size: 1rem; }
.text-lg { font-size: 1.125rem; }
.text-xl { font-size: 1.25rem; }
.text-2xl { font-size: 1.5rem; }
.text-3xl { font-size: 1.875rem; }
.text-4xl { font-size: 2.25rem; }
.text-5xl { font-size: 3rem; }

/* Font weight utilities */
.fw-light { font-weight: var(--bs-font-weight-light); }
.fw-normal { font-weight: var(--bs-font-weight-normal); }
.fw-semibold { font-weight: var(--bs-font-weight-semibold); }
.fw-bold { font-weight: var(--bs-font-weight-bold); }

/* Spacing utilities */
.space-y-xs > * + * { margin-top: var(--space-xs); }
.space-y-sm > * + * { margin-top: var(--space-sm); }
.space-y-md > * + * { margin-top: var(--space-md); }
.space-y-lg > * + * { margin-top: var(--space-lg); }
.space-y-xl > * + * { margin-top: var(--space-xl); }

/* Animation utilities */
.transition-fast { transition: all var(--transition-fast) ease; }
.transition-normal { transition: all var(--transition-normal) ease; }
.transition-slow { transition: all var(--transition-slow) ease; }
