/* Authentication Service for INGLESyLATINOS.com */

const AuthService = (function() {
  // Storage keys
  const TOKEN_KEY = 'inglesylatinos_token';
  const USER_KEY = 'inglesylatinos_user';
  const REFRESH_TOKEN_KEY = 'inglesylatinos_refresh_token';
  
  // Current user state
  let currentUser = null;
  let authToken = null;
  
  // Initialize service
  const initialize = () => {
    console.log('Initializing AuthService...');
    
    // Load stored authentication data
    loadStoredAuth();
    
    // Set up token refresh timer
    setupTokenRefresh();
    
    console.log('AuthService initialized');
  };
  
  // Load stored authentication data
  const loadStoredAuth = () => {
    try {
      const token = localStorage.getItem(TOKEN_KEY);
      const userStr = localStorage.getItem(USER_KEY);
      
      if (token && userStr) {
        authToken = token;
        currentUser = JSON.parse(userStr);
        
        // Validate token
        if (!isTokenValid(token)) {
          clearAuth();
        }
      }
    } catch (error) {
      console.error('Error loading stored auth:', error);
      clearAuth();
    }
  };
  
  // Register new user
  const register = async (userData) => {
    try {
      console.log('Registering user:', userData.email);
      
      // Validate input
      validateRegistrationData(userData);
      
      // For MVP, we'll simulate registration with local storage
      // In production, this would make an API call
      const user = {
        id: generateUserId(),
        name: userData.name,
        email: userData.email,
        createdAt: new Date().toISOString(),
        preferences: {
          voiceSpeed: 1,
          voiceType: 'female',
          practiceReminders: true,
          dailyGoal: 10
        },
        progress: {
          totalPhrases: 0,
          currentStreak: 0,
          longestStreak: 0,
          achievements: []
        }
      };
      
      // Generate token
      const token = generateToken(user);
      
      // Store authentication data
      storeAuth(token, user);
      
      // Set current user
      currentUser = user;
      authToken = token;
      
      // Update app state
      App.setCurrentUser(user);
      
      return user;
      
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  };
  
  // Login user
  const login = async (credentials) => {
    try {
      console.log('Logging in user:', credentials.email);
      
      // Validate input
      validateLoginData(credentials);
      
      // For MVP, we'll simulate login with stored user data
      // In production, this would make an API call
      const storedUsers = getStoredUsers();
      const user = storedUsers.find(u => u.email === credentials.email);
      
      if (!user) {
        throw new Error('Usuario no encontrado');
      }
      
      // In production, password would be verified on server
      // For MVP, we'll accept any password for demo purposes
      
      // Generate new token
      const token = generateToken(user);
      
      // Store authentication data
      storeAuth(token, user);
      
      // Set current user
      currentUser = user;
      authToken = token;
      
      // Update app state
      App.setCurrentUser(user);
      
      return user;
      
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  };
  
  // Logout user
  const logout = () => {
    console.log('Logging out user');
    
    // Clear authentication data
    clearAuth();
    
    // Update app state
    App.setCurrentUser(null);
    
    // Redirect to home
    Router.navigate('');
  };
  
  // Get current user
  const getCurrentUser = () => {
    return currentUser;
  };
  
  // Get authentication token
  const getToken = () => {
    return authToken;
  };
  
  // Check if token is valid
  const isTokenValid = (token) => {
    if (!token) return false;
    
    try {
      // For MVP, we'll use a simple token format
      // In production, use proper JWT validation
      const payload = JSON.parse(atob(token.split('.')[1]));
      const now = Date.now() / 1000;
      
      return payload.exp > now;
    } catch (error) {
      return false;
    }
  };
  
  // Validate registration data
  const validateRegistrationData = (data) => {
    if (!data.name || data.name.trim().length < 2) {
      throw new Error('El nombre debe tener al menos 2 caracteres');
    }
    
    if (!data.email || !isValidEmail(data.email)) {
      throw new Error('Ingresa un correo electrónico válido');
    }
    
    if (!data.password || data.password.length < 6) {
      throw new Error('La contraseña debe tener al menos 6 caracteres');
    }
    
    // Check if email already exists
    const storedUsers = getStoredUsers();
    if (storedUsers.some(u => u.email === data.email)) {
      throw new Error('Ya existe una cuenta con este correo electrónico');
    }
  };
  
  // Validate login data
  const validateLoginData = (data) => {
    if (!data.email || !isValidEmail(data.email)) {
      throw new Error('Ingresa un correo electrónico válido');
    }
    
    if (!data.password) {
      throw new Error('Ingresa tu contraseña');
    }
  };
  
  // Validate email format
  const isValidEmail = (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };
  
  // Generate user ID
  const generateUserId = () => {
    return 'user_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  };
  
  // Generate authentication token
  const generateToken = (user) => {
    // For MVP, create a simple token
    // In production, use proper JWT
    const header = btoa(JSON.stringify({ alg: 'HS256', typ: 'JWT' }));
    const payload = btoa(JSON.stringify({
      userId: user.id,
      email: user.email,
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + (24 * 60 * 60) // 24 hours
    }));
    const signature = btoa('mock_signature'); // In production, use proper signing
    
    return `${header}.${payload}.${signature}`;
  };
  
  // Store authentication data
  const storeAuth = (token, user) => {
    localStorage.setItem(TOKEN_KEY, token);
    localStorage.setItem(USER_KEY, JSON.stringify(user));
    
    // Also store user in users list for MVP
    const storedUsers = getStoredUsers();
    const existingIndex = storedUsers.findIndex(u => u.id === user.id);
    
    if (existingIndex >= 0) {
      storedUsers[existingIndex] = user;
    } else {
      storedUsers.push(user);
    }
    
    localStorage.setItem('inglesylatinos_users', JSON.stringify(storedUsers));
  };
  
  // Clear authentication data
  const clearAuth = () => {
    localStorage.removeItem(TOKEN_KEY);
    localStorage.removeItem(USER_KEY);
    localStorage.removeItem(REFRESH_TOKEN_KEY);
    
    currentUser = null;
    authToken = null;
  };
  
  // Get stored users (for MVP)
  const getStoredUsers = () => {
    try {
      const usersStr = localStorage.getItem('inglesylatinos_users');
      return usersStr ? JSON.parse(usersStr) : [];
    } catch (error) {
      return [];
    }
  };
  
  // Set up token refresh timer
  const setupTokenRefresh = () => {
    // Check token validity every 30 minutes
    setInterval(() => {
      if (authToken && !isTokenValid(authToken)) {
        console.log('Token expired, logging out');
        logout();
      }
    }, 30 * 60 * 1000);
  };
  
  // Update user data
  const updateUser = async (userData) => {
    try {
      if (!currentUser) {
        throw new Error('No hay usuario autenticado');
      }
      
      // Update current user
      currentUser = { ...currentUser, ...userData };
      
      // Store updated user
      storeAuth(authToken, currentUser);
      
      // Update app state
      App.setCurrentUser(currentUser);
      
      return currentUser;
      
    } catch (error) {
      console.error('Error updating user:', error);
      throw error;
    }
  };
  
  // Update user progress
  const updateProgress = async (progressData) => {
    try {
      if (!currentUser) {
        throw new Error('No hay usuario autenticado');
      }
      
      // Update progress
      currentUser.progress = { ...currentUser.progress, ...progressData };
      
      // Store updated user
      storeAuth(authToken, currentUser);
      
      return currentUser.progress;
      
    } catch (error) {
      console.error('Error updating progress:', error);
      throw error;
    }
  };
  
  // Public API
  return {
    initialize,
    register,
    login,
    logout,
    getCurrentUser,
    getToken,
    isTokenValid,
    updateUser,
    updateProgress
  };
})();
