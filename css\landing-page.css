/* Landing Page Specific Styles */

/* Hero Section */
.hero-section {
  background: linear-gradient(135deg, var(--bs-body-bg) 0%, var(--bs-tertiary-bg) 100%);
  padding-top: 80px; /* Account for fixed navbar */
  position: relative;
  overflow: hidden;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(55, 141, 252, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(255, 107, 107, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.hero-title {
  font-size: 3.5rem;
  font-weight: var(--bs-font-weight-bold);
  color: var(--bs-dark);
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.hero-subtitle {
  font-size: 1.25rem;
  color: var(--bs-body-color);
  margin-bottom: 2rem;
  line-height: 1.6;
}

.hero-cta {
  margin-bottom: 2rem;
}

.hero-image-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  min-height: 400px;
}

.hero-placeholder {
  background-color: var(--bs-tertiary-bg);
  border-radius: var(--bs-border-radius-xl);
  box-shadow: var(--bs-box-shadow-lg);
  padding: 3rem;
  text-align: center;
  width: 100%;
  max-width: 400px;
  transition: all var(--transition-normal) ease;
}

.hero-placeholder:hover {
  transform: translateY(-5px);
  box-shadow: 0 20px 60px rgba(55, 94, 148, 0.2);
}

.hero-icon {
  font-size: 4rem;
  color: var(--bs-primary);
  margin-bottom: 1rem;
  display: block;
}

.hero-placeholder p {
  font-size: 1.1rem;
  font-weight: var(--bs-font-weight-semibold);
  color: var(--bs-body-color);
  margin: 0;
}

/* Features Section */
.features-section {
  background-color: var(--bs-body-bg);
  position: relative;
}

.section-title {
  font-size: 2.5rem;
  font-weight: var(--bs-font-weight-bold);
  color: var(--bs-dark);
  margin-bottom: 3rem;
}

/* CTA Section */
.cta-section {
  background: linear-gradient(135deg, var(--bs-primary) 0%, var(--app-info) 100%);
  color: white;
  position: relative;
  overflow: hidden;
}

.cta-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}

.cta-container {
  position: relative;
  z-index: 1;
  padding: 3rem 0;
}

.cta-container h2 {
  font-size: 2.5rem;
  font-weight: var(--bs-font-weight-bold);
  margin-bottom: 1rem;
}

.cta-container .lead {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  opacity: 0.9;
}

.cta-container .btn {
  background-color: white;
  color: var(--bs-primary);
  border: none;
  font-weight: var(--bs-font-weight-bold);
  padding: 1rem 2rem;
  font-size: 1.1rem;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  transition: all var(--transition-normal) ease;
}

.cta-container .btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
  color: var(--bs-primary);
}

/* Footer */
.footer {
  background-color: var(--bs-tertiary-bg);
  border-top: 1px solid rgba(123, 138, 184, 0.2);
  color: var(--bs-body-color);
}

.footer a {
  color: var(--bs-body-color);
  text-decoration: none;
  transition: color var(--transition-normal) ease;
}

.footer a:hover {
  color: var(--bs-primary);
}

/* Page transitions */
.page-content {
  opacity: 1;
  transition: opacity var(--transition-normal) ease;
}

.page-content.d-none {
  opacity: 0;
}

/* Responsive design */
@media (max-width: 992px) {
  .hero-title {
    font-size: 2.5rem;
  }
  
  .hero-subtitle {
    font-size: 1.1rem;
  }
  
  .section-title {
    font-size: 2rem;
  }
  
  .cta-container h2 {
    font-size: 2rem;
  }
}

@media (max-width: 768px) {
  .hero-section {
    padding-top: 60px;
  }
  
  .hero-title {
    font-size: 2rem;
    margin-bottom: 1rem;
  }
  
  .hero-subtitle {
    font-size: 1rem;
    margin-bottom: 1.5rem;
  }
  
  .hero-cta .btn {
    display: block;
    width: 100%;
    margin-bottom: 1rem;
  }
  
  .hero-cta .btn:last-child {
    margin-bottom: 0;
  }
  
  .hero-image-container {
    min-height: 300px;
    margin-top: 2rem;
  }
  
  .hero-placeholder {
    padding: 2rem;
  }
  
  .hero-icon {
    font-size: 3rem;
  }
  
  .section-title {
    font-size: 1.75rem;
    margin-bottom: 2rem;
  }
  
  .cta-container {
    padding: 2rem 0;
  }
  
  .cta-container h2 {
    font-size: 1.75rem;
  }
  
  .cta-container .lead {
    font-size: 1.1rem;
  }
}

@media (max-width: 576px) {
  .hero-title {
    font-size: 1.75rem;
  }
  
  .hero-placeholder {
    padding: 1.5rem;
  }
  
  .hero-icon {
    font-size: 2.5rem;
  }
  
  .section-title {
    font-size: 1.5rem;
  }
  
  .cta-container h2 {
    font-size: 1.5rem;
  }
}

/* Animation enhancements */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes fadeInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-fade-in-left {
  animation: fadeInLeft 0.6s ease-out;
}

.animate-fade-in-right {
  animation: fadeInRight 0.6s ease-out;
}

/* Scroll animations */
.scroll-animate {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.6s ease-out;
}

.scroll-animate.in-view {
  opacity: 1;
  transform: translateY(0);
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .hero-placeholder:hover,
  .cta-container .btn:hover {
    transform: none;
  }
  
  .animate-fade-in-up,
  .animate-fade-in-left,
  .animate-fade-in-right {
    animation: none;
  }
  
  .scroll-animate {
    opacity: 1;
    transform: none;
    transition: none;
  }
}

/* Print styles */
@media print {
  .hero-section,
  .cta-section {
    background: white !important;
    color: black !important;
  }
  
  .navbar,
  .footer,
  .hero-cta,
  .cta-container .btn {
    display: none !important;
  }
}
