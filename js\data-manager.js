/* Data Management Service for INGLESyLATINOS.com */

const DataManager = (function() {
  // IndexedDB configuration
  const DB_NAME = 'inglesylatinos-db';
  const DB_VERSION = 1;
  
  // Store names
  const STORES = {
    PHRASES: 'phrases',
    CATEGORIES: 'categories',
    USER_DATA: 'userData',
    PENDING_SYNC: 'pendingSync'
  };
  
  // Database instance
  let db = null;
  
  // Initialize data manager
  const initialize = async () => {
    console.log('Initializing DataManager...');
    
    try {
      // Open IndexedDB
      db = await openDatabase();
      
      // Initialize default data
      await initializeDefaultData();
      
      console.log('DataManager initialized successfully');
      
    } catch (error) {
      console.error('Error initializing DataManager:', error);
      throw error;
    }
  };
  
  // Open IndexedDB database
  const openDatabase = () => {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(DB_NAME, DB_VERSION);
      
      request.onerror = () => {
        reject(new Error('Failed to open database'));
      };
      
      request.onsuccess = () => {
        resolve(request.result);
      };
      
      request.onupgradeneeded = (event) => {
        const database = event.target.result;
        
        // Create phrases store
        if (!database.objectStoreNames.contains(STORES.PHRASES)) {
          const phrasesStore = database.createObjectStore(STORES.PHRASES, { keyPath: 'id' });
          phrasesStore.createIndex('category', 'category', { unique: false });
          phrasesStore.createIndex('difficulty', 'difficulty', { unique: false });
        }
        
        // Create categories store
        if (!database.objectStoreNames.contains(STORES.CATEGORIES)) {
          database.createObjectStore(STORES.CATEGORIES, { keyPath: 'id' });
        }
        
        // Create user data store
        if (!database.objectStoreNames.contains(STORES.USER_DATA)) {
          database.createObjectStore(STORES.USER_DATA, { keyPath: 'key' });
        }
        
        // Create pending sync store
        if (!database.objectStoreNames.contains(STORES.PENDING_SYNC)) {
          database.createObjectStore(STORES.PENDING_SYNC, { keyPath: 'id', autoIncrement: true });
        }
      };
    });
  };
  
  // Initialize default data
  const initializeDefaultData = async () => {
    try {
      // Check if we already have data
      const existingPhrases = await getAllPhrases();
      if (existingPhrases.length > 0) {
        console.log('Default data already exists');
        return;
      }
      
      // Add default categories
      const defaultCategories = [
        { id: 'greetings', name: 'Saludos', description: 'Frases básicas de saludo' },
        { id: 'daily', name: 'Vida Diaria', description: 'Frases para el día a día' },
        { id: 'travel', name: 'Viajes', description: 'Frases útiles para viajar' },
        { id: 'business', name: 'Negocios', description: 'Frases profesionales' },
        { id: 'food', name: 'Comida', description: 'Frases sobre comida y restaurantes' }
      ];
      
      for (const category of defaultCategories) {
        await addCategory(category);
      }
      
      // Add default phrases
      const defaultPhrases = [
        {
          id: 'phrase_1',
          english: 'Hello, how are you?',
          phonetic: '/ˈhɛloʊ haʊ ɑr ju/',
          spanish: 'Hola, ¿cómo estás?',
          category: 'greetings',
          difficulty: 'beginner',
          createdAt: new Date().toISOString()
        },
        {
          id: 'phrase_2',
          english: 'Nice to meet you',
          phonetic: '/naɪs tu mit ju/',
          spanish: 'Mucho gusto',
          category: 'greetings',
          difficulty: 'beginner',
          createdAt: new Date().toISOString()
        },
        {
          id: 'phrase_3',
          english: 'What time is it?',
          phonetic: '/wʌt taɪm ɪz ɪt/',
          spanish: '¿Qué hora es?',
          category: 'daily',
          difficulty: 'beginner',
          createdAt: new Date().toISOString()
        },
        {
          id: 'phrase_4',
          english: 'Where is the bathroom?',
          phonetic: '/wɛr ɪz ðə ˈbæθˌrum/',
          spanish: '¿Dónde está el baño?',
          category: 'travel',
          difficulty: 'beginner',
          createdAt: new Date().toISOString()
        },
        {
          id: 'phrase_5',
          english: 'I would like to order',
          phonetic: '/aɪ wʊd laɪk tu ˈɔrdər/',
          spanish: 'Me gustaría ordenar',
          category: 'food',
          difficulty: 'intermediate',
          createdAt: new Date().toISOString()
        }
      ];
      
      for (const phrase of defaultPhrases) {
        await addPhrase(phrase);
      }
      
      console.log('Default data initialized');
      
    } catch (error) {
      console.error('Error initializing default data:', error);
    }
  };
  
  // Generic database operations
  const performTransaction = (storeName, mode, operation) => {
    return new Promise((resolve, reject) => {
      if (!db) {
        reject(new Error('Database not initialized'));
        return;
      }
      
      const transaction = db.transaction([storeName], mode);
      const store = transaction.objectStore(storeName);
      
      transaction.oncomplete = () => {
        resolve();
      };
      
      transaction.onerror = () => {
        reject(transaction.error);
      };
      
      operation(store, resolve, reject);
    });
  };
  
  // Phrase operations
  const addPhrase = async (phrase) => {
    return performTransaction(STORES.PHRASES, 'readwrite', (store, resolve, reject) => {
      // Ensure phrase has required fields
      if (!phrase.id) {
        phrase.id = generateId();
      }
      
      phrase.createdAt = phrase.createdAt || new Date().toISOString();
      phrase.updatedAt = new Date().toISOString();
      
      const request = store.add(phrase);
      
      request.onsuccess = () => {
        resolve(phrase);
      };
      
      request.onerror = () => {
        reject(request.error);
      };
    });
  };
  
  const updatePhrase = async (phrase) => {
    return performTransaction(STORES.PHRASES, 'readwrite', (store, resolve, reject) => {
      phrase.updatedAt = new Date().toISOString();
      
      const request = store.put(phrase);
      
      request.onsuccess = () => {
        resolve(phrase);
      };
      
      request.onerror = () => {
        reject(request.error);
      };
    });
  };
  
  const deletePhrase = async (phraseId) => {
    return performTransaction(STORES.PHRASES, 'readwrite', (store, resolve, reject) => {
      const request = store.delete(phraseId);
      
      request.onsuccess = () => {
        resolve(true);
      };
      
      request.onerror = () => {
        reject(request.error);
      };
    });
  };
  
  const getPhrase = async (phraseId) => {
    return performTransaction(STORES.PHRASES, 'readonly', (store, resolve, reject) => {
      const request = store.get(phraseId);
      
      request.onsuccess = () => {
        resolve(request.result);
      };
      
      request.onerror = () => {
        reject(request.error);
      };
    });
  };
  
  const getAllPhrases = async () => {
    return performTransaction(STORES.PHRASES, 'readonly', (store, resolve, reject) => {
      const request = store.getAll();
      
      request.onsuccess = () => {
        resolve(request.result || []);
      };
      
      request.onerror = () => {
        reject(request.error);
      };
    });
  };
  
  const getPhrasesByCategory = async (categoryId) => {
    return performTransaction(STORES.PHRASES, 'readonly', (store, resolve, reject) => {
      const index = store.index('category');
      const request = index.getAll(categoryId);
      
      request.onsuccess = () => {
        resolve(request.result || []);
      };
      
      request.onerror = () => {
        reject(request.error);
      };
    });
  };
  
  // Category operations
  const addCategory = async (category) => {
    return performTransaction(STORES.CATEGORIES, 'readwrite', (store, resolve, reject) => {
      if (!category.id) {
        category.id = generateId();
      }
      
      const request = store.add(category);
      
      request.onsuccess = () => {
        resolve(category);
      };
      
      request.onerror = () => {
        reject(request.error);
      };
    });
  };
  
  const getAllCategories = async () => {
    return performTransaction(STORES.CATEGORIES, 'readonly', (store, resolve, reject) => {
      const request = store.getAll();
      
      request.onsuccess = () => {
        resolve(request.result || []);
      };
      
      request.onerror = () => {
        reject(request.error);
      };
    });
  };
  
  // User data operations
  const setUserData = async (key, value) => {
    return performTransaction(STORES.USER_DATA, 'readwrite', (store, resolve, reject) => {
      const request = store.put({ key, value, updatedAt: new Date().toISOString() });
      
      request.onsuccess = () => {
        resolve(value);
      };
      
      request.onerror = () => {
        reject(request.error);
      };
    });
  };
  
  const getUserData = async (key) => {
    return performTransaction(STORES.USER_DATA, 'readonly', (store, resolve, reject) => {
      const request = store.get(key);
      
      request.onsuccess = () => {
        resolve(request.result ? request.result.value : null);
      };
      
      request.onerror = () => {
        reject(request.error);
      };
    });
  };
  
  // Utility functions
  const generateId = () => {
    return 'id_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  };
  
  // Search phrases
  const searchPhrases = async (query) => {
    const allPhrases = await getAllPhrases();
    const lowercaseQuery = query.toLowerCase();
    
    return allPhrases.filter(phrase => 
      phrase.english.toLowerCase().includes(lowercaseQuery) ||
      phrase.spanish.toLowerCase().includes(lowercaseQuery) ||
      phrase.phonetic.toLowerCase().includes(lowercaseQuery)
    );
  };
  
  // Public API
  return {
    initialize,
    
    // Phrase operations
    addPhrase,
    updatePhrase,
    deletePhrase,
    getPhrase,
    getAllPhrases,
    getPhrasesByCategory,
    searchPhrases,
    
    // Category operations
    addCategory,
    getAllCategories,
    
    // User data operations
    setUserData,
    getUserData
  };
})();
